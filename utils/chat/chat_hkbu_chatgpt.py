import os
import time
import aiohttp
from dotenv import load_dotenv

load_dotenv()
hkbu_chatgpt_api_key = os.environ.get("HKBU_CHATGPT_API_KEY")


def parse_hkbu_chatgpt_api_res(session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    try:
        # Attempt to parse the response as if it's in the expected dictionary format.
        db_record = {
            "session_id": session_id,
            "chatbot_id": chatbot_id,
            "api_provider": "HKBU ChatGPT",
            "api_id": res.get("id", ""),
            "api_object": res.get("object", ""),
            "api_created": res.get("created", 0),
            "api_model": res.get("model", ""),
            "api_message_content": res.get("choices", [])[0].get("message", {}).get("content", ""),
            "api_message_role": res.get("choices", [])[0].get("message", {}).get("role", ""),
            "api_finish_reason": res.get("choices", [])[0].get("finish_reason", ""),
            "api_prompt_tokens": res.get("usage", {}).get("prompt_tokens", 0),
            "api_completion_tokens": res.get("usage", {}).get("completion_tokens", 0),
            "api_total_tokens": res.get("usage", {}).get("total_tokens", 0),
            "api_system_fingerprint": res.get("system_fingerprint", ""),
            "time_elapsed": time_elapsed
        }
    except AttributeError as e:
        print(f"AttributeError encountered: {e}. 'res' type: {type(res)}")
        # If the response is not in the expected dictionary format, return None.
        db_record = None
    except Exception as e:
        print(f"Exception encountered: {e}. 'res' type: {type(res)}")
        # If an unexpected exception occurs, return None.
        db_record = None

    return db_record


async def call_hkbu_chatgpt_api(conversation_list, model_name="gpt-35-turbo", temperature=0.7):
    basic_url = "https://chatgpt.hkbu.edu.hk/general/rest"
    api_version = "2024-02-15-preview"
    url = f"{basic_url}/deployments/{model_name}/chat/completions/?api-version={api_version}"
    headers = {'Content-Type': 'application/json', 'api-key': hkbu_chatgpt_api_key}
    payload = {'messages': conversation_list, 'temperature': temperature}

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    return 'Error', response.status, await response.text()
    except aiohttp.ClientError as e:
        return 'Error:', e


async def chat_by_hkbu_chatgpt_api(conversation_list, model_name="gpt-35-turbo", temperature=0.7):
    print("Use HKBU ChatGPT API")

    start_time = time.time()

    res = ""
    try:
        # Generate response by accessing HKBU ChatGPT API
        res = await call_hkbu_chatgpt_api(
            conversation_list=conversation_list,
            model_name=model_name,
            temperature=temperature
        )
        if isinstance(res, tuple) and res[0] == 'Error':
            chat_response = res
        else:
            chat_response = res["choices"][0]["message"]["content"]
    except Exception as e:
        chat_response = 'Error:', e, res

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time elapsed:", time_elapsed, "seconds")

    return chat_response, res, time_elapsed
