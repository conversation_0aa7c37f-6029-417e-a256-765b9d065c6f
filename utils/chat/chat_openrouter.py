import os
import time
import json
import aiohttp
from dotenv import load_dotenv
import asyncio

load_dotenv()
openrouter_api_key = os.environ.get("OPENROUTER_API_KEY")


def parse_openrouter_api_res(session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    try:
        # Attempt to parse the response as if it's in the expected dictionary format.
        db_record = {
            "session_id": session_id,
            "chatbot_id": chatbot_id,
            "api_provider": "OpenRouter",
            "api_message_content": res.get("choices", [])[0].get("message", {}).get("content", ""),
            "api_message_role": res.get("choices", [])[0].get("message", {}).get("role", ""),
            "api_finish_reason": res.get("choices", [])[0].get("finish_reason", ""),
            "api_model": res.get("model", ""),
            "api_id": res.get("id", ""),
            "api_object": res.get("object", ""),
            "api_created": res.get("created", 0),
            "api_prompt_tokens": res.get("usage", {}).get("prompt_tokens", 0),
            "api_completion_tokens": res.get("usage", {}).get("completion_tokens", 0),
            "api_total_tokens": res.get("usage", {}).get("total_tokens", 0),
            "api_total_cost": res.get("usage", {}).get("total_cost", 0),
            "time_elapsed": time_elapsed
        }
    except AttributeError as e:
        print(f"AttributeError encountered: {e}. 'res' type: {type(res)}")
        # If the response is not in the expected dictionary format, return None.
        db_record = None
    except Exception as e:
        print(f"Exception encountered: {e}. 'res' type: {type(res)}")
        # If an unexpected exception occurs, return None.
        db_record = None

    return db_record


async def call_openrouter_api(conversation_list, model_name="openai/gpt-3.5-turbo-0125", temperature=0.7, response_format=None):
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {openrouter_api_key}",
        "HTTP-Referer": "https://bit.ly/CustomisedChat",  # Optional, for including your app on openrouter.ai rankings.
        "X-Title": "CustomisedChat",  # Optional. Shows in rankings on openrouter.ai.
    }
    payload = {
        "model": model_name,
        "messages": conversation_list,
        "temperature": temperature,
        "response_format": response_format,
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    return 'Error', response.status, await response.text()
    except aiohttp.ClientError as e:
        return 'Error:', e


async def call_openrouter_api_streaming(conversation_list, model_name="openai/gpt-4o-2024-11-20", temperature=0.7, response_format=None):
    """
    Call OpenRouter API with streaming enabled and yield chunks of the response as they arrive.
    
    Args:
        conversation_list: List of conversation messages
        model_name: The model to use
        temperature: Temperature parameter for response generation
        response_format: Optional response format specification
        
    Yields:
        Chunks of the response text as they arrive
    """
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {openrouter_api_key}",
        "HTTP-Referer": "https://bit.ly/CustomisedChat",
        "X-Title": "CustomisedChat",
        "Content-Type": "application/json",
    }
    payload = {
        "model": model_name,
        "messages": conversation_list,
        "temperature": temperature,
        "response_format": response_format,
        "stream": True,
    }

    full_response = ""
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    # Process the streaming response
                    async for line in response.content:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            if data == '[DONE]':
                                break
                            
                            try:
                                data_obj = json.loads(data)
                                if "choices" in data_obj and len(data_obj["choices"]) > 0:
                                    content = data_obj["choices"][0]["delta"].get("content")
                                    if content:
                                        full_response += content
                                        yield content
                                else:
                                    yield f"Warning: Unexpected response format: {data_obj}"
                            except json.JSONDecodeError:
                                pass
                            except Exception as e:
                                yield f"Error processing chunk: {str(e)}"
                else:
                    error_text = await response.text()
                    yield f"Error: {response.status} - {error_text}"
    except aiohttp.ClientError as e:
        yield f"Connection Error: {str(e)}"
    except asyncio.CancelledError:
        yield {"type": "cancelled", "content": full_response}
        return
    
    # Final yield with the complete response
    yield {"type": "full_response", "content": full_response}


async def chat_by_openrouter_api_streaming(conversation_list, model_name="openai/gpt-4o-2024-11-20", temperature=0.7, response_format=None):
    """
    Generate a streaming chat response using the OpenRouter API.
    
    Args:
        conversation_list: List of conversation messages
        model_name: The model to use
        temperature: Temperature parameter for response generation
        response_format: Optional response format specification
        
    Yields:
        Chunks of the response text as they arrive
    """
    print("Use OpenRouter API (Streaming)")
    start_time = time.time()
    
    full_response = ""
    
    try:
        async for chunk in call_openrouter_api_streaming(
            conversation_list=conversation_list,
            model_name=model_name,
            temperature=temperature,
            response_format=response_format,
        ):
            # Check if this is the final yield with the full response
            if isinstance(chunk, dict) and chunk.get("type") == "full_response":
                full_response = chunk.get("content", "")
                end_time = time.time()
                time_elapsed = end_time - start_time
                print("Time Elapsed:", time_elapsed, "seconds")
                
                # Create a response object similar to the non-streaming version
                res = {"choices": [{"message": {"content": full_response}}]}
                
                # Final yield with the complete response and metadata
                yield "", res, time_elapsed
                return
            
            # Check for error messages
            if isinstance(chunk, str) and (chunk.startswith("Error") or chunk.startswith("Connection Error")):
                yield chunk, None, 0
                return
            
            # Regular chunk of text
            full_response += chunk
            # print(f"DEBUG - Yielding to frontend: {repr(chunk)}")
            yield chunk, None, 0
            
    except Exception as e:
        yield f"System Error: {str(e)}", None, 0


async def chat_by_openrouter_api(conversation_list, model_name="openai/gpt-4o-2024-11-20", temperature=0.7, response_format=None):
    print("Use OpenRouter API")
    start_time = time.time()

    res = None
    try:
        # Generate response by accessing OpenRouter API
        res = await call_openrouter_api(
            conversation_list=conversation_list,
            model_name=model_name,
            temperature=temperature,
            response_format=response_format,
        )
        print("OpenRouter API Response:", res)

        # Check if res is a tuple containing error information from call_openrouter_api
        if isinstance(res, tuple) and res[0] == 'Error':
            error_message = f"API Connection Error: {res[1]}"
            return error_message, res, 0

        # Check for error in response
        if isinstance(res, dict):
            if 'error' in res:
                error_info = res.get('error', {})
                error_message = error_info.get('message', 'Unknown error occurred')
                if 'metadata' in error_info:
                    metadata = error_info['metadata']
                    if 'raw' in metadata and 'data' in metadata['raw']:
                        try:
                            raw_error = json.loads(metadata['raw']['data'])
                            if 'error' in raw_error:
                                error_message = raw_error['error'].get('message', error_message)
                        except json.JSONDecodeError:
                            pass
                return f"API Error: {error_message}", res, 0

            # Check for valid response structure
            if not res.get("choices") or len(res["choices"]) == 0:
                return "API Error: No response choices available", res, 0

            # Extract the chat response
            chat_response = res["choices"][0]["message"]["content"]
            
        else:
            return "API Error: Invalid response format", res, 0

    except Exception as e:
        return f"System Error: {str(e)}", res, 0

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time Elapsed:", time_elapsed, "seconds")

    return chat_response, res, time_elapsed
