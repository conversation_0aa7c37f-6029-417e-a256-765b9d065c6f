import os
from sqlalchemy import create_engine, event, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables with explicit encoding
try:
    load_dotenv(encoding='utf-8')
    logger.info("Environment variables loaded with UTF-8 encoding")
except Exception as e:
    logger.error(f"Failed to load .env file with UTF-8: {e}")
    # Try without explicit encoding as fallback
    try:
        load_dotenv()
        logger.info("Environment variables loaded with default encoding")
    except Exception as e2:
        logger.error(f"Failed to load .env file completely: {e2}")

DATABASE_URL = os.getenv("DATABASE_URL")

# Debug DATABASE_URL (without showing sensitive info)
if DATABASE_URL:
    # Show first 20 chars and last 20 chars, mask the middle
    url_preview = DATABASE_URL[:20] + "..." + DATABASE_URL[-20:] if len(DATABASE_URL) > 50 else DATABASE_URL[:20] + "..."
    logger.info(f"DATABASE_URL loaded: {url_preview}")
    
    # Check for problematic characters
    try:
        DATABASE_URL.encode('utf-8')
        logger.info("DATABASE_URL encoding check passed")
    except UnicodeEncodeError as e:
        logger.error(f"DATABASE_URL contains invalid characters: {e}")
else:
    logger.error("DATABASE_URL not found in environment variables")

# Don't force client_encoding in URL - let the engine handle it
# This helps avoid the "server didn't return client encoding" error
if DATABASE_URL and "client_encoding=utf8" in DATABASE_URL:
    # Remove explicit client_encoding from URL if present
    DATABASE_URL = DATABASE_URL.replace("&client_encoding=utf8", "").replace("?client_encoding=utf8", "")
    # Clean up any double separators
    DATABASE_URL = DATABASE_URL.replace("?&", "?")
    logger.info("Removed explicit client_encoding from DATABASE_URL")

# Alternative test connection using urllib.parse
def test_raw_connection():
    """Test connection using raw psycopg2 to isolate the issue"""
    try:
        import psycopg2
        from urllib.parse import urlparse, unquote
        
        parsed = urlparse(DATABASE_URL)
        
        # Try to decode the password properly
        password = parsed.password
        if password:
            try:
                # URL decode the password first
                password = unquote(password)
                logger.info("Password URL decoded successfully")
            except Exception as e:
                logger.warning(f"Password URL decode failed: {e}")
        
        logger.info(f"Connecting to host: {parsed.hostname}:{parsed.port}")
        logger.info(f"Database: {parsed.path[1:]}")
        logger.info(f"User: {parsed.username}")
        logger.info(f"Password length: {len(password) if password else 0}")
        
        # Try connection with various strategies - no explicit encoding first
        base_params = {
            'host': parsed.hostname,
            'port': parsed.port,
            'database': parsed.path[1:],  # Remove leading slash
            'user': parsed.username,
            'password': password,
            'sslmode': 'require',
            'connect_timeout': '30',
            'keepalives_idle': '30',
            'keepalives_interval': '10',
            'keepalives_count': '5',
        }
        
        logger.info("Attempting connection without explicit encoding...")
        try:
            conn = psycopg2.connect(**base_params)
            logger.info("Connection successful without explicit encoding")
        except Exception as e:
            logger.warning(f"Default connection failed: {e}")
            logger.info("Trying with explicit UTF-8 encoding...")
            try:
                connection_params = {**base_params, 'client_encoding': 'utf8'}
                conn = psycopg2.connect(**connection_params)
                logger.info("UTF-8 connection successful")
            except Exception as e2:
                logger.warning(f"UTF-8 also failed: {e2}")
                logger.info("Trying minimal connection...")
                try:
                    # Try with minimal parameters
                    minimal_params = {
                        'host': parsed.hostname,
                        'port': parsed.port,
                        'database': parsed.path[1:],
                        'user': parsed.username,
                        'password': password,
                        'sslmode': 'require',
                        'connect_timeout': '30'
                    }
                    conn = psycopg2.connect(**minimal_params)
                    logger.info("Minimal connection successful")
                except Exception as e3:
                    logger.error(f"All connection attempts failed: {e3}")
                    raise
        
        with conn.cursor() as cur:
            try:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                logger.info("Query executed successfully")
            except UnicodeDecodeError as e:
                logger.warning(f"Query execution failed with encoding error: {e}")
                # Try a different query that doesn't involve problematic data
                try:
                    cur.execute("SELECT version()")
                    result = cur.fetchone()
                    logger.info("Alternative query (SELECT version()) successful")
                except UnicodeDecodeError:
                    logger.warning("Even version query failed, but connection is established")
                    # Connection works, just can't read some data
                    pass
            
        conn.close()
        logger.info("Raw psycopg2 connection successful (with encoding workarounds)")
        return True
        
    except UnicodeDecodeError as e:
        logger.error(f"Unicode decode error: {e}")
        logger.error(f"Problematic bytes at position {e.start}-{e.end}: {repr(e.object[e.start:e.end])}")
        logger.error("Try URL-encoding your password in the DATABASE_URL")
        return False
    except Exception as e:
        logger.error(f"Raw psycopg2 connection failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        return False

# Create engine with fallback encoding strategy
def create_database_engine():
    """Create SQLAlchemy engine with encoding fallback strategy"""
    
    # Try creating engine without immediate connection test
    engines_to_try = [
        {
            "name": "Default (no encoding)",
            "connect_args": {
                "sslmode": "require",
                "connect_timeout": 30,
                "application_name": "bytewise-backend",
                "keepalives_idle": "30",
                "keepalives_interval": "10",
                "keepalives_count": "5",
            }
        },
        {
            "name": "UTF-8 via options only",
            "connect_args": {
                "sslmode": "require",
                "connect_timeout": 30,
                "application_name": "bytewise-backend",
                "options": "-c client_encoding=utf8",
                "keepalives_idle": "30",
                "keepalives_interval": "10",
                "keepalives_count": "5",
            }
        },
        {
            "name": "Minimal connection",
            "connect_args": {
                "sslmode": "require",
                "connect_timeout": 30,
                "application_name": "bytewise-backend",
                "keepalives_idle": "30",
                "keepalives_interval": "10",
                "keepalives_count": "5",
            }
        }
    ]
    
    last_error = None
    
    for engine_config in engines_to_try:
        try:
            logger.info(f"Creating SQLAlchemy engine with {engine_config['name']}...")
            engine = create_engine(
                DATABASE_URL,
                poolclass=QueuePool,
                pool_size=5,  # Reduced pool size for Supabase connection pooler
                max_overflow=10,  # Reduced overflow
                pool_pre_ping=True,
                pool_recycle=1800,  # Recycle connections every 30 minutes
                pool_timeout=30,  # Wait up to 30 seconds for a connection
                connect_args=engine_config["connect_args"],
                echo=os.getenv("DEBUG", "false").lower() == "true"
            )
            
            # Test the engine with a simple query
            try:
                with engine.connect() as conn:
                    result = conn.execute(text("SELECT 1"))
                    if result.scalar() == 1:
                        logger.info(f"{engine_config['name']} engine created and tested successfully")
                        return engine
            except Exception as test_error:
                # If connection test fails, but engine was created, keep trying other encodings
                logger.warning(f"{engine_config['name']} engine created but connection test failed: {test_error}")
                last_error = test_error
                continue
                
        except Exception as e:
            logger.warning(f"{engine_config['name']} engine creation failed: {e}")
            last_error = e
            continue
    
    # If all encoding strategies failed, create a minimal engine that doesn't test connection
    logger.error("🚨 All encoding strategies failed during connection testing!")
    logger.warning("⚠️ Creating minimal engine without connection testing - application will start but database operations may fail")
    logger.error("💡 RECOMMENDED SOLUTIONS:")
    logger.error("   1. Check your DATABASE_URL credentials")
    logger.error("   2. Verify Supabase database is accessible")
    logger.error("   3. Check network connectivity to Supabase")
    logger.error("   4. Create a fresh Supabase project if encoding issues persist")
    
    try:
        # Create minimal engine without testing
        minimal_engine = create_engine(
            DATABASE_URL,
            poolclass=QueuePool,
            pool_size=3,  # Very small pool for minimal engine
            max_overflow=5,
            pool_pre_ping=False,  # Disable pre-ping to avoid immediate connection
            pool_recycle=1800,
            pool_timeout=30,
            connect_args={
                "sslmode": "require",
                "connect_timeout": 30,
                "application_name": "bytewise-minimal",
                "keepalives_idle": "30",
                "keepalives_interval": "10", 
                "keepalives_count": "5",
                # No client_encoding parameter - let PostgreSQL handle it
            },
            echo=False
        )
        logger.warning(f"⚠️ Minimal engine created - last error was: {last_error}")
        return minimal_engine
    except Exception as e:
        logger.error(f"❌ Even minimal engine creation failed: {e}")
        raise e

# Global engine variable
engine = None

def get_engine():
    """Get database engine, creating it lazily if needed"""
    global engine
    if engine is None:
        engine = create_database_engine()
    return engine

def get_session_local():
    """Get SessionLocal, creating it lazily if needed"""
    return sessionmaker(autocommit=False, autoflush=False, bind=get_engine())

# For backward compatibility, create a lazy SessionLocal
class LazySessionLocal:
    def __call__(self):
        return get_session_local()()
    
    def __getattr__(self, name):
        return getattr(get_session_local(), name)

SessionLocal = LazySessionLocal()

Base = declarative_base()


# Event listener to handle database connections (will be attached lazily)
def set_connection_settings(dbapi_connection, connection_record):
    """Configure connection settings for better performance with error handling"""
    try:
        # Skip connection settings for Supabase to avoid SSL issues
        # Supabase already handles timezone and other settings properly
        logger.debug("Skipping connection settings for Supabase")
    except Exception as e:
        # If any connection setup fails, log but don't fail the connection
        logger.warning(f"Connection setup failed: {e}")

def setup_engine_events():
    """Setup event listeners for the engine"""
    engine = get_engine()
    # Only add the event listener if it hasn't been added already
    if not hasattr(engine, '_bytewise_events_setup'):
        event.listens_for(engine, "connect")(set_connection_settings)
        engine._bytewise_events_setup = True

def create_tables_if_needed():
    """Create database tables if they don't exist - called lazily"""
    try:
        engine = get_engine()
        if not hasattr(engine, '_tables_created'):
            logger.info("Creating database tables...")
            Base.metadata.create_all(bind=engine)
            engine._tables_created = True
            logger.info("✅ Database tables created successfully")
            return True
    except Exception as e:
        logger.warning(f"Failed to create tables: {e}")
        return False
    return True


# Dependency to get a DB session
def get_db():
    """
    Database session dependency for FastAPI routes.
    Provides automatic session management with proper cleanup.
    """
    # Ensure engine events are setup
    setup_engine_events()
    
    # Try to create tables on first database access if needed
    create_tables_if_needed()
    
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        error_msg = str(e)
        if "SSL connection has been closed unexpectedly" in error_msg:
            logger.warning(f"SSL connection error (will retry): {e}")
        elif "server didn't return client encoding" in error_msg:
            logger.warning(f"Client encoding error (Supabase issue): {e}")
        else:
            logger.error(f"Database session error: {e}")
        try:
            db.rollback()
        except Exception as rollback_error:
            logger.warning(f"Failed to rollback after error: {rollback_error}")
        raise
    finally:
        try:
            db.close()
        except Exception as close_error:
            logger.warning(f"Failed to close database session: {close_error}")


# Database health check function
def check_database_connection() -> bool:
    """
    Check if database connection is working properly.
    Returns True if connection is successful, False otherwise.
    """
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        logger.info("Database connection successful")
        return True
    except UnicodeDecodeError as e:
        logger.warning(f"Unicode decode error in database connection: {e}")
        # If we have encoding issues but engine was created, connection likely works
        try:
            db = SessionLocal()
            # Try a simpler query
            db.execute(text("SELECT current_database()"))
            db.close()
            logger.info("Database connection successful (with encoding workarounds)")
            return True
        except Exception:
            logger.error("Database connection failed even with workarounds")
            return False
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        return False


def get_database_info() -> Dict[str, Any]:
    """
    Get detailed database information including version, timezone, and table count.
    Returns a dictionary with database information.
    """
    try:
        db = SessionLocal()
        
        # Get database version
        version_result = db.execute(text("SELECT version()"))
        version = version_result.scalar()
        
        # Get timezone
        tz_result = db.execute(text("SHOW timezone"))
        timezone = tz_result.scalar()
        
        # Get current time
        time_result = db.execute(text("SELECT NOW()"))
        current_time = time_result.scalar()
        
        # Get table count
        tables_result = db.execute(text("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """))
        table_count = tables_result.scalar()
        
        # Get table names
        table_names_result = db.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """))
        table_names = [row[0] for row in table_names_result]
        
        # Get connection pool status
        engine = get_engine()
        pool_status = {
            "size": engine.pool.size(),
            "checked_in": engine.pool.checkedin(),
            "checked_out": engine.pool.checkedout(),
            "overflow": engine.pool.overflow(),
            "invalid": engine.pool.invalid(),
        }
        
        db.close()
        
        return {
            "status": "connected",
            "version": version.split(',')[0] if version else "Unknown",
            "timezone": timezone,
            "current_time": current_time.isoformat() if current_time else None,
            "table_count": table_count,
            "tables": table_names,
            "pool_status": pool_status,
            "database_url_preview": DATABASE_URL[:30] + "..." if DATABASE_URL else None
        }
        
    except SQLAlchemyError as e:
        logger.error(f"Failed to get database info: {e}")
        return {
            "status": "error",
            "error": str(e),
            "database_url_preview": DATABASE_URL[:30] + "..." if DATABASE_URL else None
        }
    except Exception as e:
        logger.error(f"Unexpected error getting database info: {e}")
        return {
            "status": "error",
            "error": f"Unexpected error: {str(e)}",
            "database_url_preview": DATABASE_URL[:30] + "..." if DATABASE_URL else None
        }


def validate_database_setup() -> Dict[str, Any]:
    """
    Comprehensive database setup validation.
    Returns validation results with detailed information.
    """
    validation_results = {
        "overall_status": "unknown",
        "checks": {},
        "recommendations": []
    }
    
    # Check 1: Environment variable
    if not DATABASE_URL:
        validation_results["checks"]["environment"] = {
            "status": "failed",
            "message": "DATABASE_URL environment variable not found"
        }
        validation_results["recommendations"].append("Set DATABASE_URL in your .env file")
    else:
        validation_results["checks"]["environment"] = {
            "status": "passed",
            "message": f"DATABASE_URL found: {DATABASE_URL[:30]}..."
        }
    
    # Check 2a: Engine creation
    try:
        test_engine = get_engine()
        if test_engine:
            validation_results["checks"]["engine_creation"] = {
                "status": "passed", 
                "message": "SQLAlchemy engine created successfully"
            }
        else:
            validation_results["checks"]["engine_creation"] = {
                "status": "failed",
                "message": "Failed to create SQLAlchemy engine"
            }
            validation_results["recommendations"].append("Check your DATABASE_URL format and credentials")
    except Exception as e:
        validation_results["checks"]["engine_creation"] = {
            "status": "failed",
            "message": f"Engine creation failed: {str(e)}"
        }
        validation_results["recommendations"].append("Check your DATABASE_URL format and credentials")

    # Check 2b: Raw psycopg2 connection (optional test)
    try:
        raw_connection_success = test_raw_connection()
        if raw_connection_success:
            validation_results["checks"]["raw_connection"] = {
                "status": "passed",
                "message": "Raw psycopg2 connection successful"
            }
        else:
            validation_results["checks"]["raw_connection"] = {
                "status": "warning",
                "message": "Raw psycopg2 connection failed (but engine creation succeeded)"
            }
    except Exception as e:
        validation_results["checks"]["raw_connection"] = {
            "status": "warning", 
            "message": f"Raw connection test error: {str(e)} (but engine creation succeeded)"
        }

    # Check 2c: SQLAlchemy connection
    try:
        db = SessionLocal()
        result = db.execute(text("SELECT 1"))
        if result.scalar() == 1:
            validation_results["checks"]["sqlalchemy_connection"] = {
                "status": "passed",
                "message": "SQLAlchemy database connection successful"
            }
        else:
            validation_results["checks"]["sqlalchemy_connection"] = {
                "status": "failed",
                "message": "Connection established but query failed"
            }
        db.close()
    except Exception as e:
        validation_results["checks"]["sqlalchemy_connection"] = {
            "status": "failed",
            "message": f"SQLAlchemy connection failed: {str(e)}"
        }
        # Only add recommendations if engine creation also failed
        try:
            test_engine = get_engine()
            if test_engine:
                validation_results["recommendations"].append("SQLAlchemy engine works but session fails - may be encoding issues with existing data")
            else:
                validation_results["recommendations"].append("Check your DATABASE_URL format and credentials")
        except:
            validation_results["recommendations"].append("Check your DATABASE_URL format and credentials")
    
    # Check 3: SSL configuration
    if DATABASE_URL and "sslmode=require" in DATABASE_URL:
        validation_results["checks"]["ssl"] = {
            "status": "passed",
            "message": "SSL mode properly configured"
        }
    else:
        validation_results["checks"]["ssl"] = {
            "status": "warning",
            "message": "SSL mode not explicitly set to 'require'"
        }
        validation_results["recommendations"].append("Add '?sslmode=require' to your DATABASE_URL for security")
    
    # Check 4: Table creation capability
    try:
        engine = get_engine()
        Base.metadata.create_all(bind=engine)
        validation_results["checks"]["table_creation"] = {
            "status": "passed",
            "message": "Table creation/migration successful"
        }
    except Exception as e:
        validation_results["checks"]["table_creation"] = {
            "status": "failed",
            "message": f"Table creation failed: {str(e)}"
        }
        validation_results["recommendations"].append("Check database permissions for table creation")
    
    # Determine overall status
    failed_checks = [check for check in validation_results["checks"].values() if check["status"] == "failed"]
    if failed_checks:
        validation_results["overall_status"] = "failed"
    elif any(check["status"] == "warning" for check in validation_results["checks"].values()):
        validation_results["overall_status"] = "warning"
    else:
        validation_results["overall_status"] = "passed"
    
    return validation_results


def print_database_setup_guide():
    """Print Supabase connection setup guide to console"""
    print("\n" + "="*60)
    print("📚 SUPABASE 连接配置指南")
    print("="*60)
    
    print("\n1️⃣ 获取Supabase数据库连接字符串:")
    print("   • 登录 https://supabase.com")
    print("   • 进入你的项目")
    print("   • 点击 Settings → Database")
    print("   • 在 Connection info 部分找到 Connection string")
    print("   • 选择 'URI' 模式")
    
    print("\n2️⃣ 创建 .env 文件并添加以下内容:")
    print("""
# Supabase数据库连接配置
DATABASE_URL=postgresql://postgres.xxxxxxxxxxxxxxxxxxxx:[YOUR_PASSWORD]@aws-0-[region].pooler.supabase.com:6543/postgres?sslmode=require

# 或者使用直连模式（端口5432）
# DATABASE_URL=postgresql://postgres.xxxxxxxxxxxxxxxxxxxx:[YOUR_PASSWORD]@db.xxxxxxxxxxxxxxxxxxxx.supabase.co:5432/postgres?sslmode=require

# 调试模式（可选）
DEBUG=false
    """)
    
    print("3️⃣ 连接字符串格式说明:")
    print("   • Connection pooler (推荐): 端口 6543")
    print("   • Direct connection: 端口 5432")
    print("   • 替换 [YOUR_PASSWORD] 为你的实际密码")
    print("   • 密码可能包含特殊字符，需要URL编码")
    
    print("\n4️⃣ 常见问题:")
    print("   • 密码包含特殊字符: 使用URL编码")
    print("   • 连接超时: 检查网络和防火墙设置")
    print("   • SSL错误: 确保使用 sslmode=require")
    
    print("\n5️⃣ 启动和测试:")
    print("   启动应用: python main.py")
    print("   或: uvicorn main:app --host 0.0.0.0 --port 8000 --reload")
    print("   测试连接: http://localhost:8000/health")
    print("   详细状态: http://localhost:8000/database/status")
    print("="*60) 