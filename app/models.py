from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>olean, DateTime, Date, ForeignKey, Float, Integer, Text, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
from pydantic import BaseModel, EmailStr
from typing import Dict, Optional, List
import datetime
import uuid
from sqlalchemy.ext.mutable import MutableList


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, unique=True, index=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    password = Column(String, nullable=False)
    personnel_id = Column(String, unique=True)
    phone_number = Column(String)
    role_name = Column(String, nullable=False)
    description = Column(JSON)
    is_active = Column(Boolean, default=False)
    activation_token = Column(String, unique=True)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class Rule(Base):
    __tablename__ = "rules"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    role_name = Column(String, nullable=False)
    rule_name = Column(String)
    rule_url = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class Enrollment(Base):
    __tablename__ = "enrollments"

    id = Column(Integer, primary_key=True, autoincrement=True)
    enrollment_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    group_id = Column(String, index=True, nullable=False)
    student_id = Column(String, index=True, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class GroupCourse(Base):
    __tablename__ = "groups_courses"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_course_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    group_id = Column(String, index=True, nullable=False)
    course_id = Column(String, index=True, nullable=False)
    teacher_id = Column(String, index=True, nullable=False)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, autoincrement=True)
    course_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    creator_user_id = Column(String, index=True, nullable=False)
    course_title = Column(String, nullable=False)
    description = Column(JSON)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class Module(Base):
    __tablename__ = "modules"

    id = Column(Integer, primary_key=True, autoincrement=True)
    module_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    course_id = Column(
        String, ForeignKey("courses.course_id"), index=True, nullable=False
    )
    module_title = Column(String, nullable=False)
    description = Column(JSON)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    chatbots = relationship("ModuleChatbot", back_populates="module")
    course = relationship("Course")


class ModuleChatbot(Base):
    __tablename__ = "modules_chatbots"

    id = Column(Integer, primary_key=True, autoincrement=True)
    module_chatbot_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    module_id = Column(
        String, ForeignKey("modules.module_id"), index=True, nullable=False
    )
    chatbot_id = Column(
        String, ForeignKey("chatbots_v2.chatbot_id"), index=True, nullable=False
    )
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    module = relationship("Module", back_populates="chatbots")
    chatbot = relationship("Chatbot", back_populates="modules")


class Chatbot(Base):
    __tablename__ = "chatbots_v2"

    id = Column(Integer, primary_key=True, autoincrement=True)
    chatbot_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    creator_user_id = Column(
        String, ForeignKey("users.user_id"), index=True, nullable=False
    )
    chatbot_name = Column(String, nullable=False)
    chatbot_unique_name = Column(String, unique=True, nullable=False)
    model_name = Column(String, nullable=False)
    system_prompt = Column(Text)
    welcome_prompt = Column(Text)
    temperature = Column(Float)
    type_name = Column(String)
    description = Column(JSON)
    knowledge_base_persist_directory = Column(String)
    knowledge_base_file_paths = Column(ARRAY(String))
    knowledge_base_file_names = Column(ARRAY(String))
    knowledge_base_embedding_model = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    creator = relationship("User")
    modules = relationship("ModuleChatbot", back_populates="chatbot")
    sessions = relationship("ChatSession", back_populates="chatbot")


class ChatSession(Base):
    __tablename__ = "chat_sessions_v2"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    chatbot_id = Column(
        String, ForeignKey("chatbots_v2.chatbot_id"), index=True, nullable=False
    )
    user_id = Column(String, ForeignKey("users.user_id"), index=True, nullable=False)
    module_id = Column(
        String, ForeignKey("modules.module_id"), index=True, nullable=False
    )
    session_name = Column(String)
    session_index = Column(Integer)
    conversation = Column(ARRAY(JSON))
    description = Column(JSON)
    script = Column(JSON)
    vimeo_url = Column(String)
    uploaded_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    chatbot = relationship("Chatbot", back_populates="sessions")
    module = relationship("Module")
    user = relationship("User")


class ChatSessionSharing(Base):
    __tablename__ = "chat_sessions_sharing"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_sharing_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    session_id = Column(String, index=True, nullable=False)
    session_name = Column(String)
    course_title = Column(String)
    module_title = Column(String)
    chatbot_id = Column(String, index=True, nullable=False)
    chatbot_name = Column(String)
    conversation = Column(ARRAY(JSON))
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))


class ChatbotResponse(Base):
    __tablename__ = "chatbot_responses_v2"

    response_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, index=True, nullable=False)
    chatbot_id = Column(String, index=True, nullable=False)
    api_provider = Column(String)
    model_name = Column(String)
    prompt_tokens = Column(Integer)
    completion_tokens = Column(Integer)
    total_tokens = Column(Integer)
    time_elapsed = Column(Float)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))


class TtiResponse(Base):
    __tablename__ = "tti_responses_v2"

    response_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, index=True, nullable=False)
    chatbot_id = Column(String, index=True, nullable=False)
    api_provider = Column(String)
    model_name = Column(String)
    revised_prompt = Column(String)
    image_url = Column(String)
    time_elapsed = Column(Float)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))


class DocResponse(Base):
    __tablename__ = "doc_responses"

    response_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, index=True, nullable=False)
    chatbot_id = Column(String, index=True, nullable=False)
    api_provider = Column(String)
    api_version = Column(String)
    model_id = Column(String)
    filename = Column(String)
    file_extension = Column(String)
    file_path = Column(String)
    file_size = Column(Integer)
    original_filename = Column(String)
    file_content = Column(String)
    doc_time_elapsed = Column(Float)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))


class OcrResponse(Base):
    __tablename__ = 'ocr_responses'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, index=True, nullable=False)
    chatbot_id = Column(String, index=True, nullable=False)
    api_provider = Column(String)
    api_version = Column(String)
    model_id = Column(String)
    filename = Column(String)
    file_extension = Column(String)
    file_path = Column(String)
    file_size = Column(Integer)
    original_filename = Column(String)
    ocr_result = Column(String)
    ocr_time_elapsed = Column(Float)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, autoincrement=True)
    notification_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    course_id = Column(
        String, ForeignKey("courses.course_id"), index=True, nullable=False
    )
    creator_user_id = Column(String, index=True, nullable=False)
    notification_title = Column(String)
    description = Column(JSON)
    # is_read = Column(Boolean, default=False)  # Temporarily commented out - database column not yet created
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    course = relationship("Course")


class InternalMessage(Base):
    __tablename__ = "internal_messages"

    id = Column(Integer, primary_key=True, autoincrement=True)
    message_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    session_id = Column(String, index=True, nullable=False)
    sender_user_id = Column(
        String, ForeignKey("users.user_id"), index=True, nullable=False
    )
    receiver_user_id = Column(
        String, ForeignKey("users.user_id"), index=True, nullable=False
    )
    message_content = Column(Text)
    message_type = Column(String)
    is_read = Column(Boolean)
    is_sent_to_chatbot = Column(Boolean)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)
    sender = relationship("User", foreign_keys=[sender_user_id])
    receiver = relationship("User", foreign_keys=[receiver_user_id])


class Group(Base):
    __tablename__ = "groups"

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(
        String, unique=True, index=True, default=lambda: str(uuid.uuid4())
    )
    group_name = Column(String, nullable=False)
    description = Column(JSON)
    creator_user_id = Column(
        String, ForeignKey("users.user_id"), index=True, nullable=False
    )
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime, nullable=True)


class Avatar(Base):
    __tablename__ = "avatars"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    creator_user_id = Column(
        String, ForeignKey("users.user_id"), nullable=False
    )  # 注意字段名是 creator_user_id
    avatar_name = Column(Text, nullable=False)
    knowledge_base = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime)


class AvatarConversations(Base):
    __tablename__ = "avatar_conversations"
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, nullable=False)
    user_id = Column(String, ForeignKey("users.user_id"), nullable=False)
    avatar_id = Column(String, ForeignKey("avatars.id"), nullable=False)
    module_id = Column(String, nullable=False)
    conversation = Column(MutableList.as_mutable(ARRAY(JSON)))
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime)


class ModuleAvatar(Base):
    __tablename__ = "modules_avatars"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    module_id = Column(String, ForeignKey("modules.module_id"), nullable=False)
    avatar_id = Column(String, ForeignKey("avatars.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(
        DateTime,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
    )
    deleted_at = Column(DateTime)


class UserCreate(BaseModel):
    username: str
    full_name: str
    email: EmailStr
    password: str
    personnel_id: str
    phone_number: Optional[str] = None
    role_name: str
    description: Optional[Dict] = {}


class UserResponse(BaseModel):
    user_id: str
    username: str
    full_name: str
    email: EmailStr
    personnel_id: str
    phone_number: Optional[str] = None
    role_name: str
    description: Optional[Dict] = {}

    class Config:
        from_attributes = True


class UserInfoResponse(BaseModel):
    user_id: str
    username: str
    full_name: str
    email: EmailStr
    role_name: str
    personnel_id: str
    phone_number: Optional[str] = None

    class Config:
        from_attributes = True


# View model for module_chatbot_item view
class ModuleChatbotItemView(Base):
    __tablename__ = "module_chatbot_item"

    # Use module_id and module_chatbot_created_at as composite primary key
    module_id = Column(String, primary_key=True)
    course_id = Column(String)
    module_title = Column(String)
    description = Column(JSON)
    module_created_at = Column(DateTime)
    module_deleted_at = Column(DateTime)
    module_chatbot_created_at = Column(DateTime, primary_key=True)
    module_chatbot_deleted_at = Column(DateTime)
    chatbot_id = Column(String, primary_key=True)
    chatbot_name = Column(String)
    chatbot_created_at = Column(DateTime)
    chatbot_deleted_at = Column(DateTime)
    creator_user_id = Column(String)
    creator_user_full_name = Column(String)


# View model for course_student_item view
class CourseStudentItemView(Base):
    __tablename__ = "course_student_item"

    # Use group_course_id and student_id as composite primary key
    group_course_id = Column(String, primary_key=True)
    student_id = Column(String, primary_key=True)

    course_id = Column(String)
    group_id = Column(String)
    group_name = Column(String)
    enrollment_created_at = Column(DateTime)
    enrollment_deleted_at = Column(DateTime)
    username = Column(String)
    full_name = Column(String)
    email = Column(String)
    personnel_id = Column(String)


# View model for notification_item view
class NotificationItemView(Base):
    __tablename__ = "notification_item"

    # Use student_id and notification_id as composite primary key
    student_id = Column(String, primary_key=True)
    notification_id = Column(String, primary_key=True)

    group_id = Column(String)
    course_id = Column(String)
    course_title = Column(String)
    creator_user_id = Column(String)
    creator_user_full_name = Column(String)
    notification_title = Column(String)
    description = Column(JSON)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    deleted_at = Column(DateTime)


# View model for module_avatar_item view
class ModuleAvatarItemView(Base):
    __tablename__ = "module_avatar_item"

    # Use module_id and avatar_id as composite primary key
    module_id = Column(String, primary_key=True)
    avatar_id = Column(String, primary_key=True)

    course_id = Column(String)
    module_deleted_at = Column(DateTime)
    creator_user_full_name = Column(String)
    creator_user_id = Column(String)
    avatar_created_at = Column(DateTime)
    avatar_deleted_at = Column(DateTime)
    module_avatar_deleted_at = Column(DateTime)
    avatar_name = Column(String)
