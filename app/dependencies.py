import os
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Depends
from fastapi.security import OAuth2PasswordBearer
import jwt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Secret key for JWT token
jwt_secret_key: str = os.environ.get("JWT_SECRET_KEY")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def authenticate_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, jwt_secret_key, algorithms=["HS256"])
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")