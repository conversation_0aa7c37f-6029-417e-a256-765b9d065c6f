import uuid
from typing import Dict, Any, List
from fastapi import API<PERSON>outer, Depends, HTTP<PERSON>x<PERSON>, Query
from sqlalchemy.orm import Session, joinedload
from pydantic import BaseModel
import datetime

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["chatbots"],
)

@router.get("/chatbot-session-list-by-module")
async def get_chatbot_session_list_by_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    module_chatbots = db.query(models.ModuleChatbot).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.deleted_at.is_(None)
    ).all()
    chatbot_ids = [mc.chatbot_id for mc in module_chatbots]

    if not chatbot_ids:
        return []

    chatbots = db.query(models.Chatbot).filter(
        models.Chatbot.chatbot_id.in_(chatbot_ids),
        models.Chatbot.deleted_at.is_(None)
    ).all()

    sessions = db.query(models.ChatSession).filter(
        models.ChatSession.chatbot_id.in_(chatbot_ids),
        models.ChatSession.user_id == user_id,
        models.ChatSession.deleted_at.is_(None)
    ).all()

    sessions_by_chatbot_id = {}
    for session in sessions:
        if session.chatbot_id not in sessions_by_chatbot_id:
            sessions_by_chatbot_id[session.chatbot_id] = []
        sessions_by_chatbot_id[session.chatbot_id].append(session)
    
    response = []
    for chatbot in chatbots:
        chatbot_data = {c.name: getattr(chatbot, c.name) for c in chatbot.__table__.columns}
        chatbot_data['sessions'] = sessions_by_chatbot_id.get(chatbot.chatbot_id, [])
        response.append(chatbot_data)

    return response

@router.get("/chatbot-session-list-by-module-and-chatbot")
async def get_chatbot_session_list_by_module_and_chatbot(module_id: str = Query(...), chatbot_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    module_chatbot = db.query(models.ModuleChatbot).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.chatbot_id == chatbot_id,
        models.ModuleChatbot.deleted_at.is_(None)
    ).first()

    if not module_chatbot:
        return []

    chatbot = db.query(models.Chatbot).filter(
        models.Chatbot.chatbot_id == chatbot_id,
        models.Chatbot.deleted_at.is_(None)
    ).first()

    if not chatbot:
        return []
    
    sessions = db.query(models.ChatSession).filter(
        models.ChatSession.user_id == user_id,
        models.ChatSession.chatbot_id == chatbot_id,
        models.ChatSession.module_id == module_id,
        models.ChatSession.deleted_at.is_(None)
    ).all()

    chatbot_data = {c.name: getattr(chatbot, c.name) for c in chatbot.__table__.columns}
    chatbot_data['sessions'] = sessions

    return chatbot_data


@router.get("/chatbot-usage-session-list")
async def get_chatbot_usage_session_list(student_id: str = Query(...), chatbot_id: str = Query(...), module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    sessions = db.query(models.ChatSession).filter(
        models.ChatSession.chatbot_id == chatbot_id,
        models.ChatSession.user_id == student_id,
        models.ChatSession.module_id == module_id,
        models.ChatSession.deleted_at.is_(None)
    ).all()
    return sessions

@router.get("/chatbot-usage-session-list-for-all-users")
async def get_chatbot_usage_session_list_for_all_users(chatbot_id: str = Query(...), module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")
    
    sessions = db.query(models.ChatSession).filter(
        models.ChatSession.chatbot_id == chatbot_id,
        models.ChatSession.module_id == module_id,
        models.ChatSession.deleted_at.is_(None)
    ).all()
    return sessions

@router.get("/chatbot")
async def get_chatbot(chatbot_id: str = Query(...), db: Session = Depends(get_db)):
    chatbot = db.query(models.Chatbot).filter(
        models.Chatbot.chatbot_id == chatbot_id,
        models.Chatbot.deleted_at.is_(None)
    ).first()
    if not chatbot:
        return []
    return chatbot


class AddChatbotData(BaseModel):
    model_config = {"protected_namespaces": ()}
    
    chatbot_name: str
    model_name: str
    system_prompt: str
    welcome_prompt: str
    temperature: float
    type_name: str
    description: Dict[str, Any] = {}

@router.post("/chatbot")
async def add_chatbot(chatbot_data: AddChatbotData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    unique_id = str(uuid.uuid4())[:5]
    new_chatbot = models.Chatbot(
        creator_user_id=user_id,
        chatbot_name=chatbot_data.chatbot_name,
        chatbot_unique_name="_".join(chatbot_data.chatbot_name.lower().split()) + "_" + unique_id,
        model_name=chatbot_data.model_name,
        system_prompt=chatbot_data.system_prompt,
        welcome_prompt=chatbot_data.welcome_prompt,
        temperature=chatbot_data.temperature,
        type_name=chatbot_data.type_name,
        description=chatbot_data.description,
    )
    db.add(new_chatbot)
    db.commit()
    db.refresh(new_chatbot)
    return new_chatbot


class UpdateChatbotData(BaseModel):
    model_config = {"protected_namespaces": ()}
    
    chatbot_id: str
    chatbot_name: str
    model_name: str
    system_prompt: str
    welcome_prompt: str
    temperature: float
    type_name: str
    description: Dict[str, Any] = {}

@router.put("/chatbot")
async def update_chatbot(chatbot_data: UpdateChatbotData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    chatbot = db.query(models.Chatbot).filter(
        models.Chatbot.chatbot_id == chatbot_data.chatbot_id
    ).first()
    if not chatbot:
        raise HTTPException(status_code=404, detail="Chatbot not found")

    chatbot.chatbot_name = chatbot_data.chatbot_name
    chatbot.model_name = chatbot_data.model_name
    chatbot.system_prompt = chatbot_data.system_prompt
    chatbot.welcome_prompt = chatbot_data.welcome_prompt
    chatbot.temperature = chatbot_data.temperature
    chatbot.type_name = chatbot_data.type_name
    chatbot.description = chatbot_data.description
    db.commit()
    db.refresh(chatbot)
    return chatbot


@router.get("/chatbot-list")
async def get_chatbot_list(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name == "Teacher":
        chatbots = db.query(models.Chatbot).filter(
            models.Chatbot.creator_user_id == user_id,
            models.Chatbot.deleted_at.is_(None)
        ).all()
        return chatbots
    
    elif user.role_name == "Student":
        # For students, get all chatbots from the modules they are enrolled in.
        enrollments = db.query(models.Enrollment).filter(models.Enrollment.user_id == user_id).all()
        if not enrollments:
            return []
        
        course_ids = [e.course_id for e in enrollments]
        
        modules = db.query(models.Module).filter(models.Module.course_id.in_(course_ids)).all()
        if not modules:
            return []
            
        module_ids = [m.module_id for m in modules]

        module_chatbots = db.query(models.ModuleChatbot).filter(
            models.ModuleChatbot.module_id.in_(module_ids),
            models.ModuleChatbot.deleted_at.is_(None)
        ).all()
        
        chatbot_ids = list(set([mc.chatbot_id for mc in module_chatbots]))
        if not chatbot_ids:
            return []
            
        chatbots = db.query(models.Chatbot).filter(
            models.Chatbot.chatbot_id.in_(chatbot_ids),
            models.Chatbot.deleted_at.is_(None)
        ).all()
        return chatbots

    return []

@router.get("/chatbot-list-by-module")
async def get_chatbot_list_by_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    # Get chatbot IDs that are already in the module
    module_chatbots_query = db.query(models.ModuleChatbot.chatbot_id).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.deleted_at.is_(None)
    )
    
    # Get all chatbots created by the teacher, excluding those already in the module
    chatbots = db.query(models.Chatbot).filter(
        models.Chatbot.creator_user_id == user_id,
        models.Chatbot.chatbot_id.notin_(module_chatbots_query),
        models.Chatbot.deleted_at.is_(None)
    ).all()
    
    return chatbots


@router.get("/chatbot-list-not-in-module")
async def get_chatbot_list_not_in_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    # Get chatbot IDs that are already in the module
    module_chatbots_query = db.query(models.ModuleChatbot.chatbot_id).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.deleted_at.is_(None)
    )
    
    # Get all chatbots created by the teacher, excluding those already in the module
    chatbots = db.query(models.Chatbot).filter(
        models.Chatbot.creator_user_id == user_id,
        models.Chatbot.chatbot_id.notin_(module_chatbots_query),
        models.Chatbot.deleted_at.is_(None)
    ).all()
    
    return chatbots


@router.delete("/chatbot")
async def delete_chatbot(chatbot_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Getting information about the chatbot
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user.role_name

    # Query chatbot information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to delete a chatbot")
    elif user_role == "Teacher":
        # Update the chatbot's deleted_at field to mark it as deleted
        chatbot = db.query(models.Chatbot).filter(
            models.Chatbot.chatbot_id == chatbot_id
        ).first()
        
        if chatbot:
            chatbot.deleted_at = datetime.datetime.utcnow()
            db.commit()
            db.refresh(chatbot)
            response = chatbot
        else:
            return []

    if not response:
        return []

    return response


@router.get("/modules-chatbots")
async def get_modules_chatbots(module_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query module-chatbot relationships according to the module ID (matching original Supabase logic)
    module_chatbots = db.query(models.ModuleChatbot).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.deleted_at.is_(None)
    ).all()

    if not module_chatbots:
        return []

    # Get chatbot IDs
    chatbot_ids = [mc.chatbot_id for mc in module_chatbots]
    
    if not chatbot_ids:
        return []

    # Query chatbots basic information without sessions (matching original Supabase logic)
    chatbots = db.query(models.Chatbot).filter(
        models.Chatbot.chatbot_id.in_(chatbot_ids),
        models.Chatbot.deleted_at.is_(None)
    ).all()

    if not chatbots:
        return []

    return chatbots
