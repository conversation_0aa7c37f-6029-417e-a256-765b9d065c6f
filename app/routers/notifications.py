from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import text
from pydantic import BaseModel
import datetime
from typing import Optional

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["notifications"],
)

@router.get("/notification-list")
async def get_notification_list(
    user_id: str = Depends(authenticate_user), 
    notification_type: Optional[str] = None, 
    db: Session = Depends(get_db)
):
    # Getting user role
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user.role_name
    notification_data = []

    # Query notification information according to user roles
    if user_role == "Student":
        # Student Role: Query notifications for a student from the view
        notification_items = db.query(models.NotificationItemView).filter(
            models.NotificationItemView.student_id == user_id
        ).all()
        
        # Filter the deleted_at field
        notification_data = [
            {
                "student_id": item.student_id,
                "group_id": item.group_id,
                "course_id": item.course_id,
                "course_title": item.course_title,
                "creator_user_id": item.creator_user_id,
                "creator_user_full_name": item.creator_user_full_name,
                "notification_id": item.notification_id,
                "notification_title": item.notification_title,
                "description": item.description,
                "created_at": item.created_at,
                "updated_at": item.updated_at,
                "deleted_at": item.deleted_at
            }
            for item in notification_items 
            if not item.deleted_at
        ]
        
        # Filter by notification type if specified and add course_title for display
        if notification_type:
            filtered_data = []
            for notification in notification_data:
                description = notification.get("description", {})
                if isinstance(description, dict) and description.get("type") == notification_type:
                    # course_title is already at the top level from the view
                    filtered_data.append(notification)
            notification_data = filtered_data

    elif user_role == "Teacher":
        # Teacher Role: Query notifications for a teacher with course information
        notifications = db.query(models.Notification).options(
            joinedload(models.Notification.course)
        ).filter(
            models.Notification.creator_user_id == user_id,
            models.Notification.deleted_at.is_(None)
        ).all()
        
        # Format response similar to original Supabase code
        for notification in notifications:
            n_dict = {
                "notification_id": notification.notification_id,
                "course_id": notification.course_id,
                "creator_user_id": notification.creator_user_id,
                "notification_title": notification.notification_title,
                "description": notification.description,
                "created_at": notification.created_at,
                "updated_at": notification.updated_at,
                "deleted_at": notification.deleted_at
            }
            
            # Add course_title to top level
            if notification.course and notification.course.course_title:
                n_dict["course_title"] = notification.course.course_title
            else:
                # Fallback to description.course_title if available
                description = notification.description or {}
                n_dict["course_title"] = description.get("course_title", "Unknown Course")
            
            # Filter by notification type if specified
            if notification_type:
                description = notification.description or {}
                if isinstance(description, dict) and description.get("type") == notification_type:
                    notification_data.append(n_dict)
            else:
                notification_data.append(n_dict)
    else:
        # Other roles: return empty list
        return []

    return notification_data


class AddNotificationData(BaseModel):
    course_id: str
    notification_title: str
    notification_content: str


@router.post("/notification")
async def add_notification(data: AddNotificationData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="You are not authorized to create notifications")

    # Insert notification data (compatible with original Supabase structure)
    new_notification = models.Notification(
        course_id=data.course_id,
        creator_user_id=user_id,
        notification_title=data.notification_title,
        description={"content": data.notification_content}
    )
    
    db.add(new_notification)
    db.commit()
    db.refresh(new_notification)

    return new_notification


@router.delete("/notification")
async def delete_notification(notification_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user.role_name
    notification_found = False

    if user_role == "Teacher":
        # For teachers, soft delete from notifications table
        notification = db.query(models.Notification).filter(
            models.Notification.notification_id == notification_id,
            models.Notification.creator_user_id == user_id
        ).first()

        if notification:
            notification.deleted_at = datetime.datetime.utcnow()
            db.commit()
            notification_found = True
        else:
            raise HTTPException(status_code=403, detail="Access denied to this notification")

    elif user_role == "Student":
        # For students, soft delete from the notifications table
        # WARNING: This will hide the notification for ALL students in the course.
        notification_to_delete = db.query(models.Notification).filter(
            models.Notification.notification_id == notification_id,
            models.Notification.deleted_at.is_(None) # Can't delete an already deleted notification
        ).first()

        if not notification_to_delete:
             raise HTTPException(status_code=404, detail="Notification not found")

        # Check if student is enrolled in the course of the notification to ensure permission
        enrollment = db.query(models.Enrollment).join(
            models.GroupCourse, models.Enrollment.group_id == models.GroupCourse.group_id
        ).filter(
            models.Enrollment.student_id == user_id,
            models.GroupCourse.course_id == notification_to_delete.course_id,
            models.Enrollment.deleted_at.is_(None),
            models.GroupCourse.deleted_at.is_(None)
        ).first()

        if not enrollment:
            raise HTTPException(status_code=403, detail="Access denied to this notification")

        notification_to_delete.deleted_at = datetime.datetime.utcnow()
        db.commit()
        notification_found = True

    if not notification_found:
        raise HTTPException(status_code=404, detail="Notification not found or you do not have permission to delete it")

    return {"message": "Notification deleted successfully"}
