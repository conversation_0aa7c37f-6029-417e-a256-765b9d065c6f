from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from app.database import (
    check_database_connection,
    get_database_info,
    validate_database_setup,
    print_database_setup_guide,
    create_tables_if_needed,
    get_engine,
    Base
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/database",
    tags=["Database Management"],
    responses={404: {"description": "Not found"}},
)

@router.get("/health")
async def database_health():
    """Simple database health check"""
    db_status = check_database_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "connected": db_status,
        "service": "Database Health Check"
    }

@router.get("/status")
async def database_status():
    """Detailed database status with comprehensive information"""
    try:
        db_info = get_database_info()
        
        if db_info["status"] == "error":
            raise HTTPException(status_code=503, detail=db_info)
        
        return JSONResponse(
            status_code=200,
            content={
                "service": "Database Status",
                "timestamp": db_info.get("current_time"),
                "database": {
                    "status": db_info["status"],
                    "version": db_info["version"],
                    "timezone": db_info["timezone"],
                    "url_preview": db_info["database_url_preview"]
                },
                "tables": {
                    "count": db_info["table_count"],
                    "names": db_info["tables"]
                },
                "connection_pool": db_info["pool_status"],
                "health": "operational"
            }
        )
    except Exception as e:
        logger.error(f"Database status check failed: {e}")
        raise HTTPException(
            status_code=503, 
            detail={
                "service": "Database Status",
                "status": "error",
                "error": str(e),
                "health": "degraded"
            }
        )

@router.get("/validate")
async def database_validate():
    """Comprehensive database setup validation"""
    try:
        validation_results = validate_database_setup()
        
        status_code = 200
        if validation_results["overall_status"] == "failed":
            status_code = 503
        elif validation_results["overall_status"] == "warning":
            status_code = 200  # Warnings are still OK
        
        return JSONResponse(
            status_code=status_code,
            content={
                "service": "Database Validation",
                "overall_status": validation_results["overall_status"],
                "checks": validation_results["checks"],
                "recommendations": validation_results["recommendations"],
                "summary": {
                    "total_checks": len(validation_results["checks"]),
                    "passed": len([c for c in validation_results["checks"].values() if c["status"] == "passed"]),
                    "warnings": len([c for c in validation_results["checks"].values() if c["status"] == "warning"]),
                    "failed": len([c for c in validation_results["checks"].values() if c["status"] == "failed"])
                }
            }
        )
    except Exception as e:
        logger.error(f"Database validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "service": "Database Validation",
                "status": "error",
                "error": str(e)
            }
        )

@router.get("/tables")
async def list_tables():
    """List all database tables"""
    try:
        db_info = get_database_info()
        
        if db_info["status"] == "error":
            raise HTTPException(status_code=503, detail=db_info)
        
        return {
            "service": "Table List",
            "table_count": db_info["table_count"],
            "tables": db_info["tables"],
            "timestamp": db_info.get("current_time")
        }
    except Exception as e:
        logger.error(f"Failed to list tables: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "service": "Table List",
                "status": "error",
                "error": str(e)
            }
        )

@router.get("/pool")
async def connection_pool_status():
    """Get connection pool status information"""
    try:
        db_info = get_database_info()
        
        if db_info["status"] == "error":
            raise HTTPException(status_code=503, detail=db_info)
        
        return {
            "service": "Connection Pool Status",
            "pool_status": db_info["pool_status"],
            "timestamp": db_info.get("current_time"),
            "status": "operational"
        }
    except Exception as e:
        logger.error(f"Failed to get pool status: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "service": "Connection Pool Status",
                "status": "error",
                "error": str(e)
            }
        )

@router.get("/info")
async def database_info():
    """Get basic database information"""
    try:
        db_info = get_database_info()
        
        if db_info["status"] == "error":
            raise HTTPException(status_code=503, detail=db_info)
        
        return {
            "service": "Database Info",
            "version": db_info["version"],
            "timezone": db_info["timezone"],
            "current_time": db_info.get("current_time"),
            "url_preview": db_info["database_url_preview"],
            "status": "connected"
        }
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "service": "Database Info",
                "status": "error",
                "error": str(e)
            }
        )

@router.post("/create-tables")
async def create_tables():
    """Manually create database tables - useful for testing and troubleshooting"""
    try:
        logger.info("Manual table creation requested")
        success = create_tables_if_needed()
        
        if success:
            # Get updated table info
            db_info = get_database_info()
            return {
                "service": "Create Tables",
                "status": "success",
                "message": "Database tables created successfully",
                "table_count": db_info.get("table_count", 0),
                "tables": db_info.get("tables", []),
                "timestamp": db_info.get("current_time")
            }
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "service": "Create Tables",
                    "status": "failed", 
                    "message": "Failed to create database tables",
                    "error": "Check server logs for details"
                }
            )
    except Exception as e:
        logger.error(f"Manual table creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "service": "Create Tables",
                "status": "error",
                "error": str(e),
                "message": "Manual table creation failed"
            }
        ) 