from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
import datetime

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["enrollments"],
)


class EmailEnrollmentData(BaseModel):
    group_id: str
    email: str


@router.post("/email-enrollment")
async def email_enrollment(data: EmailEnrollmentData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name not in ["Teacher", "Student"]:
        raise HTTPException(status_code=403, detail="Operation not permitted")

    student = db.query(models.User).filter(models.User.email == data.email).first()
    if not student:
        raise HTTPException(status_code=404, detail="Student not found")

    existing_enrollment = db.query(models.Enrollment).filter(
        models.Enrollment.group_id == data.group_id,
        models.Enrollment.student_id == student.user_id,
        models.Enrollment.deleted_at.is_(None)
    ).first()

    if existing_enrollment:
        raise HTTPException(status_code=400, detail="Student already enrolled in the group")

    new_enrollment = models.Enrollment(
        group_id=data.group_id,
        student_id=student.user_id
    )
    db.add(new_enrollment)
    db.commit()
    db.refresh(new_enrollment)
    return [new_enrollment]


@router.delete("/enrollment")
async def delete_enrollment(group_id: str, student_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    enrollment = db.query(models.Enrollment).filter(
        models.Enrollment.group_id == group_id,
        models.Enrollment.student_id == student_id,
        models.Enrollment.deleted_at.is_(None)
    ).first()

    if not enrollment:
        raise HTTPException(status_code=404, detail="Enrollment not found")

    enrollment.deleted_at = datetime.datetime.utcnow()
    db.commit()
    db.refresh(enrollment)
    return [enrollment]
