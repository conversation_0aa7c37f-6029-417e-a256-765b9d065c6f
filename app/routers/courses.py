from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from pydantic import BaseModel
import datetime
from dateutil import parser

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["courses"],
)


def parse_date_string(date_string: str) -> datetime.datetime:
    """Parse date string in various formats to datetime object"""
    try:
        # Try parsing with dateutil parser which handles many formats
        return parser.parse(date_string)
    except (ValueError, TypeError):
        # If that fails, try to handle the specific format we're seeing
        # '7/3/2025, 8:00:00 AM' format
        try:
            return datetime.datetime.strptime(date_string, '%m/%d/%Y, %I:%M:%S %p')
        except ValueError:
            # Last resort: try ISO format
            return datetime.datetime.fromisoformat(date_string)
    except Exception:
        raise ValueError(f"Unable to parse date string: {date_string}")


@router.get("/course-list")
async def get_course_list(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    course_data = []
    if user.role_name == "Student":
        enrollments = db.query(models.Enrollment.group_id).filter(
            models.Enrollment.student_id == user_id,
            models.Enrollment.deleted_at.is_(None)
        ).all()
        group_ids = [e.group_id for e in enrollments]

        if group_ids:
            # Query both Course and GroupCourse data to maintain compatibility
            results = db.query(models.Course, models.GroupCourse).join(
                models.GroupCourse, models.GroupCourse.course_id == models.Course.course_id
            ).filter(
                models.GroupCourse.group_id.in_(group_ids),
                models.Course.deleted_at.is_(None)
            ).all()
            
            # Format data to match Supabase structure
            course_data = []
            seen = set()
            for course, group_course in results:
                if course.course_id not in seen:
                    seen.add(course.course_id)
                    course_data.append({
                        "course_id": course.course_id,
                        "courses": {
                            "course_id": course.course_id,
                            "course_title": course.course_title,
                            "creator_user_id": course.creator_user_id,
                            "description": course.description,
                            "created_at": course.created_at.isoformat() if course.created_at else None,
                            "updated_at": course.updated_at.isoformat() if course.updated_at else None,
                            "deleted_at": course.deleted_at.isoformat() if course.deleted_at else None
                        }
                    })

    elif user.role_name == "Teacher":
        # Query both Course and GroupCourse data to maintain compatibility  
        results = db.query(models.Course, models.GroupCourse).join(
            models.GroupCourse, models.GroupCourse.course_id == models.Course.course_id
        ).filter(
            models.GroupCourse.teacher_id == user_id,
            models.Course.deleted_at.is_(None)
        ).all()
        
        # Format data to match Supabase structure
        course_data = []
        for course, group_course in results:
            course_data.append({
                "course_id": course.course_id,
                "teacher_id": group_course.teacher_id,
                "courses": {
                    "course_id": course.course_id,
                    "course_title": course.course_title,
                    "creator_user_id": course.creator_user_id,
                    "description": course.description,
                    "created_at": course.created_at.isoformat() if course.created_at else None,
                    "updated_at": course.updated_at.isoformat() if course.updated_at else None,
                    "deleted_at": course.deleted_at.isoformat() if course.deleted_at else None
                }
            })

    return course_data


@router.get("/course-info")
async def get_course_info(course_id: str = Query(...), db: Session = Depends(get_db)):
    course = db.query(models.Course).filter(models.Course.course_id == course_id, models.Course.deleted_at.is_(None)).first()
    if not course:
        return []
    return course


@router.get("/course-student-list")
async def get_course_student_list(course_id: str = Query(...), db: Session = Depends(get_db)):
    # Getting information about the course from the view
    course_student_items = db.query(models.CourseStudentItemView).filter(
        models.CourseStudentItemView.course_id == course_id
    ).all()
    
    if not course_student_items:
        return []

    # Filter the deleted_at field (deleted_at field means the student is unenrolled)
    student_data = [
        {
            "group_course_id": item.group_course_id,
            "course_id": item.course_id,
            "group_id": item.group_id,
            "group_name": item.group_name,
            "student_id": item.student_id,
            "enrollment_created_at": item.enrollment_created_at,
            "enrollment_deleted_at": item.enrollment_deleted_at,
            "username": item.username,
            "full_name": item.full_name,
            "email": item.email,
            "personnel_id": item.personnel_id
        }
        for item in course_student_items 
        if not item.enrollment_deleted_at
    ]
    
    # Filter the null student_id field (no student_id means there is no student enrolled)
    student_data = [student for student in student_data if student["student_id"]]

    return student_data


class AddCourseData(BaseModel):
    course_title: str
    group_id: str
    start_date: str
    end_date: str


@router.post("/course")
async def add_course(course_data: AddCourseData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Add a course according to user roles
    if user.role_name == "Student":
        raise HTTPException(status_code=404, detail="Student cannot add course")
    elif user.role_name == "Teacher":
        new_course = models.Course(
            creator_user_id=user_id,
            course_title=course_data.course_title
        )
        db.add(new_course)
        db.flush()  # To get new_course.course_id

        new_group_course = models.GroupCourse(
            group_id=course_data.group_id,
            course_id=new_course.course_id,
            teacher_id=user_id,
            start_date=parse_date_string(course_data.start_date),
            end_date=parse_date_string(course_data.end_date)
        )
        db.add(new_group_course)

        # Insert a default module for the course
        new_module = models.Module(
            course_id=new_course.course_id,
            module_title="Default Module"
        )
        db.add(new_module)
        db.commit()
        db.refresh(new_module)
        return new_module
    else:
        return []


class RenameCourseData(BaseModel):
    course_id: str
    course_title: str


@router.put("/course")
async def rename_course(course_data: RenameCourseData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Only teachers can rename courses")

    course = db.query(models.Course).filter(models.Course.course_id == course_data.course_id, models.Course.creator_user_id == user_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found or you are not the creator")

    course.course_title = course_data.course_title
    db.commit()
    db.refresh(course)
    return course


@router.delete("/course")
async def delete_course(course_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Only teachers can delete courses")

    now = datetime.datetime.utcnow()
    course = db.query(models.Course).filter(models.Course.course_id == course_id, models.Course.creator_user_id == user_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found or you are not the creator")

    course.deleted_at = now
    
    db.query(models.GroupCourse).filter(models.GroupCourse.course_id == course_id).update({"deleted_at": now})

    db.commit()

    return {"message": "Course deleted successfully"}
