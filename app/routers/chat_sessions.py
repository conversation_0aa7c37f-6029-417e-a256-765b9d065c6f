from datetime import datetime
from typing import List, Any
import json
from fastapi import APIRouter, Depends, HTTPException, Query, Form, File, UploadFile
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
import aiofiles
import os
from uuid import uuid4

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user
from pydantic import BaseModel
from utils.chat.chat_openrouter import chat_by_openrouter_api
from utils.doc.doc_azure import analyze_document
from utils.prompts.prompt_web_search import get_prompt_web_search
from utils.prompts.prompt_get_checklist_progress import get_prompt_checklist_progress

router = APIRouter(
    prefix="/api",
    tags=["chat_sessions"],
)

@router.get("/chat-session-by-student")
async def get_chat_session_by_student(session_id: str, student_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Permission denied")

    # Handle "latest" session_id
    if session_id.lower() == "latest":
        session = db.query(models.ChatSession).filter(
            models.ChatSession.user_id == student_id,
            models.ChatSession.deleted_at.is_(None)
        ).order_by(desc(models.ChatSession.created_at)).first()
    else:
        session = db.query(models.ChatSession).filter(
            models.ChatSession.session_id == session_id,
            models.ChatSession.user_id == student_id,
            models.ChatSession.deleted_at.is_(None)
        ).first()
    
    if not session:
        return []
    return session

@router.get("/chat-session")
async def get_chat_session(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Handle "latest" session_id
    if session_id.lower() == "latest":
        session = db.query(models.ChatSession).filter(
            models.ChatSession.user_id == user_id,
            models.ChatSession.deleted_at.is_(None)
        ).order_by(desc(models.ChatSession.created_at)).first()
    else:
        session = db.query(models.ChatSession).filter(
            models.ChatSession.session_id == session_id,
            models.ChatSession.user_id == user_id,
            models.ChatSession.deleted_at.is_(None)
        ).first()
    
    if not session:
        return []
    return session


class AddChatSessionData(BaseModel):
    chatbot_id: str
    module_id: str

@router.post("/chat-session")
async def add_chat_session(session_data: AddChatSessionData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    max_session_index = db.query(func.max(models.ChatSession.session_index)).filter(
        models.ChatSession.user_id == user_id,
        models.ChatSession.chatbot_id == session_data.chatbot_id,
        models.ChatSession.module_id == session_data.module_id,
        models.ChatSession.deleted_at.is_(None)
    ).scalar() or 0

    new_session_index = max_session_index + 1
    new_session_name = f"Session {new_session_index}"

    new_session = models.ChatSession(
        chatbot_id=session_data.chatbot_id,
        module_id=session_data.module_id,
        user_id=user_id,
        session_index=new_session_index,
        session_name=new_session_name,
    )
    db.add(new_session)
    db.commit()
    db.refresh(new_session)
    return new_session


@router.delete("/chat-session")
async def delete_chat_session(session_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    session = db.query(models.ChatSession).filter(
        models.ChatSession.session_id == session_id,
        models.ChatSession.user_id == user_id
    ).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session.deleted_at = datetime.utcnow()
    db.commit()
    db.refresh(session)
    return session

class UpdateChatSessionNameData(BaseModel):
    session_id: str
    session_name: str

@router.put("/chat-session-name")
async def update_chat_session_name(data: UpdateChatSessionNameData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    session = db.query(models.ChatSession).filter(
        models.ChatSession.session_id == data.session_id,
        models.ChatSession.user_id == user_id
    ).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session.session_name = data.session_name
    db.commit()
    db.refresh(session)
    return session

class UpdateChatSessionConversationData(BaseModel):
    session_id: str
    conversation: List[Any]

@router.put("/chat-session-conversation")
async def update_chat_session_conversation(data: UpdateChatSessionConversationData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    session = db.query(models.ChatSession).filter(
        models.ChatSession.session_id == data.session_id,
        models.ChatSession.user_id == user_id
    ).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session.conversation = data.conversation
    db.commit()
    db.refresh(session)
    return session


@router.get("/chat-session-sharing")
async def get_chat_session_sharing(session_sharing_id: str = Query(...), db: Session = Depends(get_db)):
    sharing_session = db.query(models.ChatSessionSharing).filter(
        models.ChatSessionSharing.session_sharing_id == session_sharing_id
    ).first()
    if not sharing_session:
        return []
    return sharing_session

class AddChatSessionSharingData(BaseModel):
    session_id: str
    session_name: str
    course_title: str
    module_title: str
    chatbot_id: str
    chatbot_name: str
    conversation: List[Any]

@router.post("/chat-session-sharing")
async def add_chat_session_sharing(data: AddChatSessionSharingData, db: Session = Depends(get_db)):
    new_sharing_session = models.ChatSessionSharing(**data.dict())
    db.add(new_sharing_session)
    db.commit()
    db.refresh(new_sharing_session)
    return new_sharing_session


@router.post("/chat-session-attach-file")
async def attach_file(session_id: str = Form(...), chatbot_id: str = Form(...), file: UploadFile = File(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)

    file_extension = file.filename.split('.')[-1]
    unique_filename = f"{uuid4()}.{file_extension}"
    file_path = os.path.join(uploads_dir, unique_filename)
    
    async with aiofiles.open(file_path, 'wb') as out_file:
        content = await file.read()
        await out_file.write(content)
    
    file_content, res, doc_time_elapsed = analyze_document(file_path)

    new_doc_response = models.DocResponse(
        session_id=session_id,
        chatbot_id=chatbot_id,
        api_provider="Azure Document Intelligence",
        api_version=res.api_version,
        model_id=res.model_id,
        filename=unique_filename,
        file_extension=file_extension,
        file_path=file_path,
        file_size=len(content),
        original_filename=file.filename,
        file_content=file_content,
        doc_time_elapsed=doc_time_elapsed,
    )
    db.add(new_doc_response)
    db.commit()

    return {"filename": unique_filename, "original_filename": file.filename, "file_content": file_content}

@router.post("/chat-session-ocr-image")
async def upload_image_and_ocr(session_id: str = Form(...), chatbot_id: str = Form(...), image: UploadFile = File(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)

    file_extension = image.filename.split('.')[-1]
    unique_filename = f"{uuid4()}.{file_extension}"
    file_path = os.path.join(uploads_dir, unique_filename)
    
    async with aiofiles.open(file_path, 'wb') as out_file:
        content = await image.read()
        await out_file.write(content)
        
    ocr_result, res, ocr_time_elapsed = analyze_document(file_path)

    new_ocr_response = models.OcrResponse(
        session_id=session_id,
        chatbot_id=chatbot_id,
        api_provider="Azure Document Intelligence",
        api_version=res.api_version,
        model_id=res.model_id,
        filename=unique_filename,
        file_extension=file_extension,
        file_path=file_path,
        file_size=len(content),
        original_filename=image.filename,
        ocr_result=ocr_result,
        ocr_time_elapsed=ocr_time_elapsed,
    )
    db.add(new_ocr_response)
    db.commit()

    return {"filename": unique_filename, "original_filename": image.filename, "ocr_result": ocr_result}

@router.post("/chat-session-web-search")
async def get_web_search_result(query: str = Form(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    web_search_result, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=[{
            "role": "user",
            "content": query + "\n\n" + get_prompt_web_search()
        }],
        model_name="perplexity/sonar",
        temperature=0.5
    )
    return web_search_result

class GetChecklistProgressData(BaseModel):
    session_id: str
    conversation_list: List[Any]
    checklist_items: str

@router.post("/chat-session-checklist-progress")
async def get_checklist_progress(data: GetChecklistProgressData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    combined_conversation_list = data.conversation_list + [{
        "role": "user",
        "content": get_prompt_checklist_progress() + "\n\n```\n" + data.checklist_items + "\n```"
    }]

    response_format = {
        "type": "json_schema",
        "json_schema": {
            "name": "checklist_progress",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "checklist_progress": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "item_id": {
                                    "type": "string"
                                },
                                "item_content": {
                                    "type": "string"
                                },
                                "completed": {
                                    "type": "boolean"
                                }
                            },
                            "required": ["item_id", "item_content", "completed"],
                            "additionalProperties": False
                        }
                    }
                },
                "required": ["checklist_progress"],
                "additionalProperties": False
            }
        }
    }

    checklist_progress, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=combined_conversation_list,
        model_name="openai/gpt-4.1-mini",
        temperature=0.5,
        response_format=response_format
    )
    
    if not checklist_progress:
        raise HTTPException(status_code=404, detail="No checklist progress found")
    
    session = db.query(models.ChatSession).filter(models.ChatSession.session_id == data.session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    current_description = session.description or {}
    loaded_checklist_progress = json.loads(checklist_progress)
    current_description["checklist_progress"] = loaded_checklist_progress.get('checklist_progress', [])
    
    session.description = current_description
    db.commit()

    return current_description['checklist_progress']

class GetChatSessionSummaryData(BaseModel):
    session_id: str
    conversation_list: List[Any]
    session_summary_prompt: str

@router.post("/chat-session-summary")
async def get_session_summary(data: GetChatSessionSummaryData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    combined_conversation_list = data.conversation_list + [{"role": "user", "content": data.session_summary_prompt}]

    session_summary, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=combined_conversation_list,
        model_name="openai/gpt-4.1",
        temperature=0.5
    )
    
    session = db.query(models.ChatSession).filter(models.ChatSession.session_id == data.session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
        
    current_description = session.description or {}
    if "qualitative_report" not in current_description:
        current_description["qualitative_report"] = {}
    current_description["qualitative_report"]["session_summary"] = session_summary
    
    session.description = current_description
    db.commit()
    
    return session_summary


@router.get("/chat-session-description")
async def get_chat_session_description(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Query the chat session information - no user_id restriction like in original Supabase version
    session = db.query(models.ChatSession).filter(
        models.ChatSession.session_id == session_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Return the record in the same format as original Supabase version
    return {"description": session.description}

class UpdateChatSessionDescriptionData(BaseModel):
    session_id: str
    description: dict

@router.put("/chat-session-description")
async def update_chat_session_description(data: UpdateChatSessionDescriptionData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    # Query user information (matching original Supabase logic)
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Query the chat session without user restriction (matching original Supabase logic)
    session = db.query(models.ChatSession).filter(
        models.ChatSession.session_id == data.session_id
    ).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
        
    current_description = session.description or {}
    merged_description = {
        "checklist_progress": data.description.get("checklist_progress", current_description.get("checklist_progress", [])),
        "quantitative_report": {
            "turn_count": data.description.get("quantitative_report", {}).get("turn_count", current_description.get("quantitative_report", {}).get("turn_count", 0)),
            "user_word_count": data.description.get("quantitative_report", {}).get("user_word_count", current_description.get("quantitative_report", {}).get("user_word_count", 0)),
            "chatbot_word_count": data.description.get("quantitative_report", {}).get("chatbot_word_count", current_description.get("quantitative_report", {}).get("chatbot_word_count", 0)),
            "conversation_time": data.description.get("quantitative_report", {}).get("conversation_time", current_description.get("quantitative_report", {}).get("conversation_time", 0))
        },
        "qualitative_report": {
            "session_summary": data.description.get("qualitative_report", {}).get("session_summary", current_description.get("qualitative_report", {}).get("session_summary", ""))
        },
        "user_feedback": data.description.get("user_feedback", current_description.get("user_feedback", {}))
    }
    
    session.description = merged_description
    db.commit()
    db.refresh(session)
    return session

