from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from pydantic import BaseModel
import datetime
from typing import List, Optional
import uuid

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["internal_messages"],
)

class SendInternalMessageData(BaseModel):
    session_id: str
    content: str
    recipient_role: str

class MarkReadData(BaseModel):
    session_id: str
    user_role: str

class MarkSentToChatbotData(BaseModel):
    message_id: str

class CreateRaiseHandNotificationData(BaseModel):
    course_id: str
    creator_user_id: str
    notification_title: str
    description: dict

class CreateRaiseHandNotificationSimpleData(BaseModel):
    session_id: str
    session_name: str
    course_id: str
    course_title: str
    module_id: str
    module_title: str
    chatbot_id: str

async def resolve_recipient_user_id(session_id: str, sender_role: str, course_id: str, db: Session):
    if sender_role == "Student":
        group_course = db.query(models.GroupCourse).filter(models.GroupCourse.course_id == course_id).first()
        if not group_course:
            raise HTTPException(status_code=404, detail=f"No teacher found for course {course_id}")
        return group_course.teacher_id
    else:
        session = db.query(models.ChatSession).filter(models.ChatSession.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
        return session.user_id

async def create_teacher_reply_notification(session_id: str, teacher_user_id: str, student_user_id: str, reply_content: str, db: Session):
    session = db.query(models.ChatSession).options(
        joinedload(models.ChatSession.chatbot),
        joinedload(models.ChatSession.module).joinedload(models.Module.course)
    ).filter(models.ChatSession.session_id == session_id).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    teacher = db.query(models.User).filter(models.User.user_id == teacher_user_id).first()
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")

    reply_preview = reply_content[:100] + "..." if len(reply_content) > 100 else reply_content
    
    # Convert UUID-like objects to strings to ensure JSON serializable
    def _to_str(value):
        return str(value) if isinstance(value, uuid.UUID) else value
    
    new_notification = models.Notification(
        course_id=_to_str(session.module.course_id),
        creator_user_id=_to_str(student_user_id),
        notification_title=f"Teacher {teacher.full_name} replied to your question",
        description={
            "type": "teacher_reply",
            "session_id": _to_str(session_id),
            "session_name": session.session_name,
            "module_id": _to_str(session.module_id),
            "module_title": session.module.module_title,
            "chatbot_id": _to_str(session.chatbot_id),
            "course_id": _to_str(session.module.course_id),
            "course_title": session.module.course.course_title,
            "teacher_user_id": _to_str(teacher_user_id),
            "teacher_name": teacher.full_name,
            "reply_content": reply_preview
        }
    )
    db.add(new_notification)
    db.commit()
    return new_notification.notification_id

@router.get("/internal-messages")
async def get_internal_messages(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    """
    Fetch all internal messages for a specific session with user details.
    Returns messages with sender_role and receiver_role derived from users table.
    """
    try:
        # First verify that the user has access to this session (matching original Supabase logic)
        session = db.query(models.ChatSession).filter(models.ChatSession.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_owner = session.user_id
        
        # Get user role to determine access permissions
        user = db.query(models.User).filter(models.User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user.role_name
        
        # Students can only access their own sessions, teachers can access any session
        # Convert both to strings for comparison in case of UUID vs string mismatch
        if user_role == "Student" and str(session_owner) != str(user_id):
            raise HTTPException(status_code=403, detail="Access denied to this session")

        # Query internal messages (simplified query without JOIN initially)
        messages = db.query(models.InternalMessage).filter(
            models.InternalMessage.session_id == session_id
        ).order_by(models.InternalMessage.created_at).all()
        
        if not messages:
            return []
        
        # Get unique user IDs from messages
        user_ids = set()
        for message in messages:
            user_ids.add(message.sender_user_id)
            user_ids.add(message.receiver_user_id)
        
        # Fetch user roles for all involved users
        users = db.query(models.User).filter(models.User.user_id.in_(list(user_ids))).all()
        
        # Create a mapping of user_id to role_name
        user_roles = {}
        for user_obj in users:
            user_roles[user_obj.user_id] = user_obj.role_name
        
        # Transform the response to include sender_role and receiver_role (matching original format)
        formatted_messages = []
        for message in messages:
            formatted_message = {
                "message_id": message.message_id,
                "session_id": message.session_id,
                "sender_user_id": message.sender_user_id,
                "receiver_user_id": message.receiver_user_id,
                "message_content": message.message_content,
                "message_type": message.message_type,
                "is_read": message.is_read,
                "is_sent_to_chatbot": message.is_sent_to_chatbot,
                "created_at": message.created_at,
                "sender_role": user_roles.get(message.sender_user_id),
                "receiver_role": user_roles.get(message.receiver_user_id)
            }
            formatted_messages.append(formatted_message)
        
        return formatted_messages
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        print(f"Unexpected error in get_internal_messages: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch messages: {str(e)}")

@router.post("/internal-messages")
async def send_internal_message(data: SendInternalMessageData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
        
    session = db.query(models.ChatSession).filter(models.ChatSession.session_id == data.session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    recipient_user_id = await resolve_recipient_user_id(data.session_id, user.role_name, session.module.course_id, db)
    
    new_message = models.InternalMessage(
        session_id=data.session_id,
        sender_user_id=user_id,
        receiver_user_id=recipient_user_id,
        message_content=data.content,
        message_type="text",
    )
    db.add(new_message)
    
    if user.role_name == "Teacher":
        await create_teacher_reply_notification(data.session_id, user_id, session.user_id, data.content, db)
        
    db.commit()
    db.refresh(new_message)
    return new_message

@router.put("/internal-messages/mark-read")
async def mark_messages_as_read(data: MarkReadData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    try:
        user = db.query(models.User).filter(models.User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
            
        session = db.query(models.ChatSession).filter(models.ChatSession.session_id == data.session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Students can only access their own sessions, teachers can access any session
        # Convert both to strings for comparison in case of UUID vs string mismatch
        if user.role_name == "Student" and str(session.user_id) != str(user_id):
            raise HTTPException(status_code=403, detail="Access denied to this session")

        # Mark messages as read and count updated messages
        updated_count = db.query(models.InternalMessage).filter(
            models.InternalMessage.session_id == data.session_id,
            models.InternalMessage.receiver_user_id == user_id,
            models.InternalMessage.is_read == False
        ).update({"is_read": True})
        
        db.commit()
        
        # Return format matching original Supabase version
        return {"success": True, "updated_count": updated_count}
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        print(f"Unexpected error in mark_messages_as_read: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to mark messages as read: {str(e)}")

@router.put("/internal-messages/mark-sent-to-chatbot")
async def mark_message_sent_to_chatbot(data: MarkSentToChatbotData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Only teachers can send messages to chatbot")
        
    message = db.query(models.InternalMessage).filter(
        models.InternalMessage.message_id == data.message_id
    ).first()
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    if message.is_sent_to_chatbot:
        raise HTTPException(status_code=400, detail="Message already sent to chatbot")

    message.is_sent_to_chatbot = True
    db.commit()
    return {"message": "Message marked as sent to chatbot"}

@router.post("/internal-message-notification")
async def create_raise_hand_notification(data: CreateRaiseHandNotificationSimpleData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    student = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not student or student.role_name != "Student":
        raise HTTPException(status_code=403, detail="Only students can raise hand")

    group_course = db.query(models.GroupCourse).filter(models.GroupCourse.course_id == data.course_id).first()
    if not group_course:
        raise HTTPException(status_code=404, detail="No teacher found for this course")
    teacher_user_id = group_course.teacher_id

    message = db.query(models.InternalMessage).filter(
        models.InternalMessage.session_id == data.session_id,
        models.InternalMessage.sender_user_id == user_id
    ).order_by(models.InternalMessage.created_at.desc()).first()

    message_content = "Student is asking for help"
    if message:
        full_message = message.message_content
        message_content = full_message[:100] + "..." if len(full_message) > 100 else full_message

    new_notification = models.Notification(
        course_id=data.course_id,
        creator_user_id=teacher_user_id,
        notification_title=f"Student {student.full_name} needs help",
        description={
            "type": "raise_hand",
            "session_id": data.session_id,
            "session_name": data.session_name,
            "module_id": data.module_id,
            "module_title": data.module_title,
            "chatbot_id": data.chatbot_id,
            "student_user_id": user_id,
            "student_name": student.full_name,
            "course_title": data.course_title,
            "message_content": message_content
        }
    )
    db.add(new_notification)
    db.commit()
    db.refresh(new_notification)
    return {"notification_id": new_notification.notification_id}

@router.post("/internal-messages/raise-hand-notification")
async def create_raise_hand_notification_legacy(data: CreateRaiseHandNotificationData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Student":
        raise HTTPException(status_code=403, detail="Only students can raise hand")

    new_notification = models.Notification(
        course_id=data.course_id,
        creator_user_id=data.creator_user_id,
        notification_title=data.notification_title,
        description=data.description
    )
    db.add(new_notification)
    db.commit()
    db.refresh(new_notification)
    return new_notification

@router.get("/chat-session-info")
async def get_chat_session_info(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    session = db.query(models.ChatSession).options(
        joinedload(models.ChatSession.module).joinedload(models.Module.course)
    ).filter(models.ChatSession.session_id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.get("/course-teachers")
async def get_course_teachers(course_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    teachers = db.query(models.User).join(
        models.GroupCourse, models.GroupCourse.teacher_id == models.User.user_id
    ).filter(models.GroupCourse.course_id == course_id).all()
    return teachers

@router.get("/internal-messages/raise-hand-notifications")
async def get_raise_hand_notifications(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    notifications = db.query(models.Notification).options(
        joinedload(models.Notification.course)
    ).filter(
        models.Notification.creator_user_id == user_id,
        models.Notification.description['type'].as_string() == 'raise_hand',
        models.Notification.deleted_at.is_(None)
    ).all()
    
    result = []
    for n in notifications:
        notification_dict = {
            "notification_id": n.notification_id,
            "course_id": n.course_id,
            "creator_user_id": n.creator_user_id,
            "notification_title": n.notification_title,
            "description": n.description,
            "created_at": n.created_at,
            "deleted_at": n.deleted_at,
        }
        if n.course:
            notification_dict['course_title'] = n.course.course_title
        elif n.description and isinstance(n.description, dict) and 'course_title' in n.description:
            notification_dict['course_title'] = n.description['course_title']
        else:
            notification_dict['course_title'] = "Unknown Course"
        result.append(notification_dict)
        
    return result

@router.delete("/internal-messages/raise-hand-notification/{notification_id}")
async def dismiss_raise_hand_notification(notification_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")
        
    notification = db.query(models.Notification).filter(
        models.Notification.notification_id == notification_id,
        models.Notification.creator_user_id == user_id
    ).first()
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
        
    notification.deleted_at = datetime.datetime.utcnow()
    db.commit()
    return {"message": "Notification dismissed"}

@router.get("/teacher-reply-notifications") 
async def get_teacher_reply_notifications(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Student":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    notifications = db.query(models.Notification).filter(
        models.Notification.creator_user_id == user_id,
        models.Notification.description['type'].as_string() == 'teacher_reply',
        models.Notification.deleted_at.is_(None)
    ).all()
    
    result = []
    for n in notifications:
        notification_dict = {
            "notification_id": n.notification_id,
            "course_id": n.course_id,
            "creator_user_id": n.creator_user_id,
            "notification_title": n.notification_title,
            "description": n.description,
            "created_at": n.created_at,
            "deleted_at": n.deleted_at,
        }
        if n.description and isinstance(n.description, dict) and 'course_title' in n.description:
            notification_dict['course_title'] = n.description['course_title']
        else:
            notification_dict['course_title'] = "Unknown Course"
        result.append(notification_dict)

    return result

@router.get("/internal-messages/unsent-to-chatbot")
async def get_unsent_internal_messages(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    messages = db.query(models.InternalMessage).filter(
        models.InternalMessage.session_id == session_id,
        models.InternalMessage.is_sent_to_chatbot == False
    ).order_by(models.InternalMessage.created_at).all()
    return messages

@router.put("/internal-messages/mark-all-sent-to-chatbot")
async def mark_all_messages_sent_to_chatbot(session_id: str = Query(...), user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    db.query(models.InternalMessage).filter(
        models.InternalMessage.session_id == session_id
    ).update({"is_sent_to_chatbot": True})
    db.commit()
    return {"message": "All messages marked as sent to chatbot"}
    