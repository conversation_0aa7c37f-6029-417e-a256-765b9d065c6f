from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from pydantic import BaseModel
import datetime

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["modules"],
)

@router.get("/module-list")
async def get_module_list(course_id: str = Query(...), db: Session = Depends(get_db)):
    modules = db.query(models.Module).filter(
        models.Module.course_id == course_id,
        models.Module.deleted_at.is_(None)
    ).all()
    return modules


@router.get("/module-chatbot-list")
async def get_module_chatbot_list(course_id: str = Query(...), db: Session = Depends(get_db)):
    module_chatbot_items = db.query(models.ModuleChatbotItemView).filter(
        models.ModuleChatbotItemView.course_id == course_id
    ).all()
    modules_dict = {}
    for item in module_chatbot_items:
        module_id = item.module_id
        # If the module is not in the dictionary and not deleted, initialize the dictionary
        if module_id not in modules_dict and not item.module_deleted_at:
            modules_dict[module_id] = {
                'module_id': module_id,
                'course_id': item.course_id,
                'module_title': item.module_title,
                'description': item.description,
                'created_at': item.module_created_at,
                'chatbots': []
            }
        # If the module and chatbot are not deleted, append
        # If the dictionary has been initialized, append
        # Note: The dictionary must exist (i.e., the module is not deleted and initialized) to append
        if (
            module_id in modules_dict and
            not item.module_deleted_at and
            not item.chatbot_deleted_at and
            not item.module_chatbot_deleted_at and
            item.chatbot_id
        ):
            modules_dict[module_id]['chatbots'].append({
                'chatbot_id': item.chatbot_id,
                'chatbot_name': item.chatbot_name,
                'created_at': item.chatbot_created_at,
                'creator_user_id': item.creator_user_id,
                'creator_user_full_name': item.creator_user_full_name,
            })
    modules_with_chatbots = list(modules_dict.values())
    return modules_with_chatbots


class ModuleChatbotData(BaseModel):
    module_id: str
    chatbot_ids: List[str]

@router.post("/module-chatbots")
async def add_module_chatbots(module_chatbot: ModuleChatbotData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted for students")

    new_relations = []
    for chatbot_id in module_chatbot.chatbot_ids:
        new_relation = models.ModuleChatbot(
            module_id=module_chatbot.module_id,
            chatbot_id=chatbot_id
        )
        new_relations.append(new_relation)
    
    db.add_all(new_relations)
    db.commit()

    for rel in new_relations:
        db.refresh(rel)

    return new_relations


@router.delete("/module-chatbot")
async def delete_module_chatbot(module_id: str, chatbot_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted for students")

    relation = db.query(models.ModuleChatbot).filter(
        models.ModuleChatbot.module_id == module_id,
        models.ModuleChatbot.chatbot_id == chatbot_id,
        models.ModuleChatbot.deleted_at.is_(None)
    ).first()

    if not relation:
        raise HTTPException(status_code=404, detail="Relation not found")

    relation.deleted_at = datetime.datetime.utcnow()
    db.commit()
    db.refresh(relation)
    return relation


@router.delete("/module")
async def delete_module(module_id: str, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted for students")

    module = db.query(models.Module).filter(models.Module.module_id == module_id, models.Module.deleted_at.is_(None)).first()
    if not module:
        raise HTTPException(status_code=404, detail="Module not found")

    module.deleted_at = datetime.datetime.utcnow()
    db.commit()
    db.refresh(module)
    return module


class CreateModuleData(BaseModel):
    course_id: str
    module_title: str

@router.post("/module")
async def add_module(module_data: CreateModuleData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted for students")

    new_module = models.Module(
        course_id=module_data.course_id,
        module_title=module_data.module_title
    )
    db.add(new_module)
    db.commit()
    db.refresh(new_module)
    return new_module


class RenameModuleData(BaseModel):
    module_id: str
    module_title: str

@router.put("/module")
async def rename_module(module_data: RenameModuleData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted for students")

    module = db.query(models.Module).filter(models.Module.module_id == module_data.module_id, models.Module.deleted_at.is_(None)).first()
    if not module:
        raise HTTPException(status_code=404, detail="Module not found")

    module.module_title = module_data.module_title
    db.commit()
    db.refresh(module)
    return module
