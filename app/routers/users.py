from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from .. import models
from ..database import get_db
from ..dependencies import authenticate_user


router = APIRouter(
    prefix="/api",
    tags=["users"],
)


@router.get("/user-info")
async def get_user_info(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Manually create user info to handle UUID conversion
    user_info = {
        "user_id": str(user.user_id),
        "username": user.username,
        "full_name": user.full_name,
        "email": user.email,
        "role_name": user.role_name,
        "personnel_id": user.personnel_id
    }
    return user_info


@router.get("/protected-route")
async def protected_route(user_id: str = Depends(authenticate_user)):
    return {"message": f"Hello, user {user_id}"}
