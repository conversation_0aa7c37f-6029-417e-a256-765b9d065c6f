from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel, EmailStr, constr
from sqlalchemy.orm import Session
import bcrypt
import jwt
import datetime
import uuid
import resend
import os

from .. import models
from ..database import get_db
from ..dependencies import jwt_secret_key, authenticate_user

# Configure the Resend API key
resend_api_key: str = os.environ.get("RESEND_API_KEY")
resend.api_key = resend_api_key

router = APIRouter(
    prefix="/api",
    tags=["auth"],
)


class RegisterUser(BaseModel):
    fullName: str
    email: EmailStr
    password: constr(min_length=6)
    role: str
    personnelId: str


class LoginUser(BaseModel):
    email: EmailStr
    password: constr(min_length=6)


class ChangePassword(BaseModel):
    current_password: constr(min_length=6)
    new_password: constr(min_length=6)


@router.post("/register")
async def register_user(user: RegisterUser, db: Session = Depends(get_db)):
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email is already registered")

    hashed_password = bcrypt.hashpw(user.password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    unique_id = str(uuid.uuid4())[:5]
    user_id = str(uuid.uuid4())
    activation_token = str(uuid.uuid4())

    try:
        new_user = models.User(
            user_id=user_id,
            username="_".join(user.fullName.lower().split()) + "_" + unique_id,
            full_name=user.fullName,
            email=user.email,
            password=hashed_password,
            personnel_id=user.personnelId,
            role_name=user.role,
            activation_token=activation_token,
            description={}
        )
        db.add(new_user)
        
        # Commit the user first to ensure it exists in the database
        # before creating related records that reference it
        db.commit()

        # Send activation email
        send_activation_email(user.fullName, user.email, activation_token)

        # Now create role-specific records that reference the user
        if user.role == "Student":
            new_enrollment = models.Enrollment(
                group_id="8e58a61c-d034-4cbb-a0c0-8bd527dc890b",
                student_id=user_id
            )
            db.add(new_enrollment)
            db.commit()
        elif user.role == "Teacher":
            new_group_course = models.GroupCourse(
                group_id="8e58a61c-d034-4cbb-a0c0-8bd527dc890b",
                course_id="0b99ca04-70c7-47cc-98c5-c39e6fbad057",
                teacher_id=user_id,
                start_date=datetime.date.today(),
                end_date=datetime.date.today() + datetime.timedelta(days=365)
            )
            db.add(new_group_course)
            db.commit()

        return {"message": "User created successfully"}
    
    except Exception as e:
        # Rollback any partial changes
        db.rollback()
        # Log the actual error for debugging
        print(f"Registration error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create user. Please try again.")


BYTEWISE_BASE_URL = "https://chat.hkbu.life"


def send_activation_email(full_name: str, email: str, activation_token: str):
    # Construct the activation URL
    activation_url = f"{BYTEWISE_BASE_URL}/#/activate/{activation_token}"

    params: resend.Emails.SendParams = {
        "from": "Bytewise <<EMAIL>>",
        "to": [email],
        "subject": "Welcome to Bytewise! Let's Get Your Account Set Up 🚀",
        "html": f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activate Your Bytewise Account</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f7f7f7; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
        <h2 style="color: #2c3e50;">Hi {full_name},</h2>

        <p>We're thrilled to have you join <strong>Bytewise</strong>! Let's get you started and unlock the AI-assisted, personalized teaching and learning experience we've prepared for you.</p>

        <p>To activate your account and get everything set up, please click the link below:</p>

        <a href="{activation_url}" style="display: inline-block; background-color: #3498db; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px; margin-top: 20px;">Activate My Account</a>

        <p>If the button above doesn't work, copy and paste the following link into your browser:</p>
        
        <p style="word-wrap: break-word;"><a href="{activation_url}" style="color: #3498db;">{activation_url}</a></p>

        <p>Thank you for choosing <strong>Bytewise</strong>! We can't wait to have you on board.</p>

        <p style="margin-top: 20px;">Best regards,</p>
        <p><strong>Bytewise Team</strong></p>
    </div>
</body>
</html>
"""
    }

    # Send email
    resend.Emails.send(params)

    return {"message": "Activation email sent successfully"}


@router.post("/login")
async def login_user(user: LoginUser, db: Session = Depends(get_db)):
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if not db_user:
        raise HTTPException(status_code=400, detail="Invalid email or password")

    if not db_user.is_active:
        raise HTTPException(status_code=403, detail="User is not activated, please check your email for the activation link")

    if not bcrypt.checkpw(user.password.encode('utf-8'), db_user.password.encode('utf-8')):
        raise HTTPException(status_code=400, detail="Invalid email or password")

    payload = {
        "user_id": str(db_user.user_id),
        "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    token = jwt.encode(payload, jwt_secret_key, algorithm="HS256")

    # Manually create user data to handle UUID conversion
    user_data = {
        "user_id": str(db_user.user_id),
        "username": db_user.username,
        "full_name": db_user.full_name,
        "email": db_user.email,
        "personnel_id": db_user.personnel_id,
        "role_name": db_user.role_name,
        "description": db_user.description or {}
    }
    return {"message": "User logged in successfully", "token": token, "userData": user_data}


class ActivateAccountData(BaseModel):
    token: str


@router.put("/activate")
async def activate_account(data: ActivateAccountData, db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.activation_token == data.token).first()
    if not user:
        raise HTTPException(status_code=404, detail="Invalid activation token")

    user.is_active = True
    user.activation_token = None
    db.commit()

    return {"message": "Account activated successfully, redirecting to login page in 5 seconds..."}


@router.put("/change-password")
async def change_password(password_data: ChangePassword, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not bcrypt.checkpw(password_data.current_password.encode('utf-8'), user.password.encode('utf-8')):
        raise HTTPException(status_code=400, detail="Invalid current password")

    hashed_password = bcrypt.hashpw(password_data.new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    user.password = hashed_password
    db.commit()

    return {"message": "Password updated successfully"}
