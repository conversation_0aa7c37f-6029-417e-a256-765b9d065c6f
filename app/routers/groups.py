from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
import datetime

from .. import models
from ..database import get_db
from ..dependencies import authenticate_user

router = APIRouter(
    prefix="/api",
    tags=["groups"],
)

@router.get("/joined-student-group-list")
async def get_joined_student_group_list(user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role_name != "Student":
        return []

    enrollments = db.query(models.Enrollment).join(models.Group).filter(
        models.Enrollment.student_id == user_id,
        models.Enrollment.deleted_at.is_(None),
        models.Group.deleted_at.is_(None)
    ).all()

    response = []
    for enrollment in enrollments:
        response.append({
            "enrollment_id": enrollment.enrollment_id,
            "group_id": enrollment.group.group_id,
            "group_name": enrollment.group.group_name,
            "description": enrollment.group.description,
        })
    return response

class UpdateGroupIDData(BaseModel):
    group_course_id: str
    group_id: str

@router.put("/group-course")
async def update_group_id(data: UpdateGroupIDData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    group_course = db.query(models.GroupCourse).filter(
        models.GroupCourse.group_course_id == data.group_course_id
    ).first()
    if not group_course:
        raise HTTPException(status_code=404, detail="GroupCourse not found")

    group_course.group_id = data.group_id
    group_course.updated_at = datetime.datetime.utcnow()
    db.commit()
    db.refresh(group_course)
    return group_course


class CreateGroupData(BaseModel):
    group_name: str
    description_content: str

@router.post("/group")
async def create_group(data: CreateGroupData, user_id: str = Depends(authenticate_user), db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user or user.role_name != "Teacher":
        raise HTTPException(status_code=403, detail="Operation not permitted")

    new_group = models.Group(
        group_name=data.group_name,
        description={"content": data.description_content},
        creator_user_id=user_id,
    )
    db.add(new_group)
    db.commit()
    db.refresh(new_group)
    return new_group

@router.get("/group")
async def get_group(group_id: str = Query(...), db: Session = Depends(get_db)):
    group = db.query(models.Group).filter(
        models.Group.group_id == group_id,
        models.Group.deleted_at.is_(None)
    ).first()
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    return group

@router.get("/group-by-course")
async def get_group_by_course(course_id: str = Query(...), db: Session = Depends(get_db)):
    group_course = db.query(models.GroupCourse).filter(
        models.GroupCourse.course_id == course_id
    ).first()
    if not group_course:
        raise HTTPException(status_code=404, detail="Group for this course not found")

    group = db.query(models.Group).filter(
        models.Group.group_id == group_course.group_id,
        models.Group.deleted_at.is_(None)
    ).first()
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    return group
